import { defineStore } from 'pinia'
import { ref, computed, watch } from 'vue'
import type { BrowserProvider } from 'ethers'
import { STORAGE_KEYS } from '../config/constants'

export interface WalletState {
  isConnected: boolean
  address: string
  balance: string
  chainId: number
  provider: BrowserProvider | null
}

export const useWalletStore = defineStore('wallet', () => {
  // 状态
  const isConnected = ref(false)
  const address = ref('')
  const balance = ref('0')
  const chainId = ref(0)
  const provider = ref<BrowserProvider | null>(null)
  const isConnecting = ref(false)
  const lastConnectedTime = ref(0)
  const walletType = ref<'metamask' | 'walletconnect' | null>(null)
  const isLoading = ref(false)

  // 计算属性
  const shortAddress = computed(() => {
    if (!address.value) return ''
    return `${address.value.slice(0, 6)}...${address.value.slice(-4)}`
  })

  const isCorrectNetwork = computed(() => {
    const targetChainId = parseInt((import.meta as any).env.VITE_CHAIN_ID || '31337')
    return chainId.value === targetChainId
  })

  const networkName = computed(() => {
    const networks: Record<number, string> = {
      1: 'Ethereum Mainnet',
      11155111: 'Sepolia Testnet',
      31337: 'Hardhat Local',
      1337: 'Ganache Local'
    }
    return networks[chainId.value] || `Unknown Network (${chainId.value})`
  })

  const formattedBalance = computed(() => {
    const bal = parseFloat(balance.value)
    if (bal === 0) return '0'
    if (bal < 0.001) return '<0.001'
    return bal.toFixed(3)
  })

  // 方法
  const setWalletInfo = (walletInfo: Partial<WalletState>) => {
    if (walletInfo.isConnected !== undefined) isConnected.value = walletInfo.isConnected
    if (walletInfo.address) address.value = walletInfo.address
    if (walletInfo.balance) balance.value = walletInfo.balance
    if (walletInfo.chainId) chainId.value = walletInfo.chainId
    if (walletInfo.provider) provider.value = walletInfo.provider

    if (walletInfo.isConnected) {
      lastConnectedTime.value = Date.now()
      saveWalletState()
    }
  }

  const disconnect = () => {
    isConnected.value = false
    address.value = ''
    balance.value = '0'
    chainId.value = 0
    provider.value = null
    walletType.value = null
    lastConnectedTime.value = 0

    // 清除本地存储
    clearWalletState()
  }

  const setConnecting = (connecting: boolean) => {
    isConnecting.value = connecting
  }

  const setLoading = (loading: boolean) => {
    isLoading.value = loading
  }

  const setWalletType = (type: 'metamask' | 'walletconnect' | null) => {
    walletType.value = type
    if (type) {
      localStorage.setItem('wallet_type', type)
    } else {
      localStorage.removeItem('wallet_type')
    }
  }

  // 保存钱包状态到本地存储
  const saveWalletState = () => {
    if (!isConnected.value || !address.value) return

    const stateToSave = {
      address: address.value,
      chainId: chainId.value,
      balance: balance.value,
      lastConnectedTime: lastConnectedTime.value,
      walletType: walletType.value
    }

    localStorage.setItem(STORAGE_KEYS.WALLET_STATE, JSON.stringify(stateToSave))
  }

  // 从本地存储加载钱包状态
  const loadWalletState = () => {
    try {
      const savedState = localStorage.getItem(STORAGE_KEYS.WALLET_STATE)
      if (savedState) {
        const parsedState = JSON.parse(savedState)

        // 验证数据有效性
        if (parsedState && typeof parsedState === 'object') {
          address.value = parsedState.address || ''
          chainId.value = parsedState.chainId || 0
          balance.value = parsedState.balance || '0'
          lastConnectedTime.value = parsedState.lastConnectedTime || 0
          walletType.value = parsedState.walletType || null

          // 检查是否在24小时内连接过，且地址有效
          const isRecent = Date.now() - lastConnectedTime.value < 24 * 60 * 60 * 1000
          const isValidAddress = address.value && address.value.length === 42 && address.value.startsWith('0x')

          if (isRecent && isValidAddress) {
            // 不直接设置为已连接，而是标记为可能连接状态
            // 实际连接状态需要通过钱包服务验证
            console.log('发现最近的钱包连接记录:', address.value)
          }
        }
      }
    } catch (error) {
      console.error('加载钱包状态失败:', error)
      // 清除损坏的数据
      localStorage.removeItem(STORAGE_KEYS.WALLET_STATE)
    }
  }

  // 清除钱包状态
  const clearWalletState = () => {
    localStorage.removeItem(STORAGE_KEYS.WALLET_STATE)
    localStorage.removeItem('wallet_connected')
    localStorage.removeItem('wallet_type')
  }

  // 验证钱包状态
  const validateWalletState = () => {
    if (!isConnected.value) return false

    const isValidAddress = address.value && address.value.length === 42 && address.value.startsWith('0x')
    const isValidChainId = chainId.value > 0
    const isValidBalance = balance.value !== undefined

    return isValidAddress && isValidChainId && isValidBalance
  }

  // 监听状态变化，保存到本地存储
  watch([isConnected, address, balance, chainId], () => {
    if (isConnected.value) {
      saveWalletState()
    }
  })

  // 初始化时加载状态
  loadWalletState()

  return {
    // 状态
    isConnected,
    address,
    balance,
    chainId,
    provider,
    isConnecting,
    walletType,
    lastConnectedTime,
    isLoading,

    // 计算属性
    shortAddress,
    isCorrectNetwork,
    networkName,
    formattedBalance,

    // 方法
    setWalletInfo,
    disconnect,
    setConnecting,
    setLoading,
    setWalletType,
    saveWalletState,
    loadWalletState,
    clearWalletState,
    validateWalletState
  }
})
