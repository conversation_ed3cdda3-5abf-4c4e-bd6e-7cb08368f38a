import { describe, it, expect, vi, beforeEach } from 'vitest'
import { nextTick } from 'vue'
import WalletConnect from '@/components/WalletConnect.vue'
import { mountComponent, createMockProvider } from '@/test/utils'
import { useWalletStore } from '@/stores/wallet'
import { useWallet } from '@/composables/useWallet'

describe('WalletConnect', () => {
  let mockProvider: any

  beforeEach(() => {
    mockProvider = createMockProvider()
    vi.clearAllMocks()
  })

  it('renders wallet connection options', () => {
    const wrapper = mountComponent(WalletConnect, {
      props: { show: true }
    })

    expect(wrapper.find('[data-testid="wallet-connect-modal"]').exists()).toBe(true)
    expect(wrapper.find('[data-testid="metamask-option"]').exists()).toBe(true)
    expect(wrapper.find('[data-testid="walletconnect-option"]').exists()).toBe(true)
  })

  it('shows connection modal when not connected', async () => {
    const wrapper = mountComponent(WalletConnect, {
      props: { show: true }
    })

    await nextTick()
    expect(wrapper.find('[data-testid="wallet-connect-modal"]').isVisible()).toBe(true)
  })

  it('attempts MetaMask connection when clicked', async () => {
    const wrapper = mountComponent(WalletConnect, {
      props: { show: true }
    })

    const metamaskButton = wrapper.find('[data-testid="metamask-option"]')
    await metamaskButton.trigger('click')

    // Should attempt to connect to MetaMask
    expect(wrapper.emitted('connect-wallet')).toBeTruthy()
  })

  it('handles connection errors gracefully', async () => {
    const wrapper = mountComponent(WalletConnect, {
      props: { show: true }
    })

    // Mock connection error by setting connectionError in useWallet
    const { connectionError } = useWallet()
    connectionError.value = 'Connection failed'

    await nextTick()

    expect(wrapper.find('[data-testid="error-message"]').exists()).toBe(true)
  })

  it('displays loading state during connection', async () => {
    const wrapper = mountComponent(WalletConnect, {
      props: { show: true }
    })

    // Simulate connecting state
    const metamaskButton = wrapper.find('[data-testid="metamask-option"]')
    await metamaskButton.trigger('click')

    // Check if loading spinner appears
    expect(wrapper.find('[data-testid="loading-spinner"]').exists()).toBe(true)
  })

  it('closes modal after successful connection', async () => {
    const wrapper = mountComponent(WalletConnect, {
      props: { show: true }
    })

    const metamaskButton = wrapper.find('[data-testid="metamask-option"]')
    await metamaskButton.trigger('click')
    await nextTick()

    // Should emit close event after connection attempt
    expect(wrapper.emitted('close')).toBeTruthy()
  })

  it('supports keyboard navigation', async () => {
    const wrapper = mountComponent(WalletConnect, {
      props: { show: true }
    })

    const metamaskButton = wrapper.find('[data-testid="metamask-option"]')
    await metamaskButton.trigger('keydown.enter')

    expect(wrapper.emitted('connect-wallet')).toBeTruthy()
  })

  it('is accessible with proper ARIA attributes', () => {
    const wrapper = mountComponent(WalletConnect, {
      props: { show: true }
    })

    const modal = wrapper.find('[data-testid="wallet-connect-modal"]')
    expect(modal.attributes('role')).toBe('dialog')
    expect(modal.attributes('aria-labelledby')).toBeDefined()

    const buttons = wrapper.findAll('button')
    buttons.forEach(button => {
      expect(button.attributes('aria-label')).toBeDefined()
    })
  })
})
