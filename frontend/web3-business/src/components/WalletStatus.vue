<template>
  <div class="wallet-status" data-testid="wallet-status">
    <!-- 未连接状态 -->
    <div v-if="!walletStore.isConnected" class="wallet-disconnected" data-testid="wallet-disconnected">
      <van-button
        type="primary"
        size="small"
        :loading="walletStore.isConnecting"
        data-testid="connect-button"
        aria-label="连接钱包"
        @click="showConnectModal = true"
      >
        <van-icon name="wallet" class="mr-1" />
        连接钱包
      </van-button>
    </div>

    <!-- 已连接状态 -->
    <div v-else class="wallet-connected" data-testid="wallet-connected">
      <div class="flex items-center space-x-3">
        <!-- 网络状态指示器 -->
        <div class="flex items-center" data-testid="network-info">
          <div
            class="w-2 h-2 rounded-full mr-2"
            :class="networkStatusClass"
            data-testid="network-indicator"
          ></div>
          <span class="text-xs text-gray-600" data-testid="network-name">{{ networkName }}</span>
        </div>

        <!-- 余额显示 -->
        <div class="flex items-center bg-gray-100 rounded-lg px-3 py-1" data-testid="balance-display">
          <van-icon name="gold-coin" class="text-yellow-500 mr-1" />
          <span v-if="refreshing" data-testid="balance-loading">
            <van-loading size="14px" />
          </span>
          <span v-else class="text-sm font-medium" data-testid="wallet-balance">{{ formattedBalance }}</span>
        </div>

        <!-- 地址显示 -->
        <button
          class="flex items-center bg-blue-50 rounded-lg px-3 py-1 cursor-pointer hover:bg-blue-100 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500"
          data-testid="wallet-address"
          aria-label="复制钱包地址"
          @click="copyAddress"
        >
          <van-icon name="user" class="text-blue-500 mr-1" />
          <span class="text-sm font-mono">{{ walletStore.shortAddress }}</span>
        </button>

        <!-- 网络警告 -->
        <div v-if="!walletStore.isCorrectNetwork" data-testid="network-warning" class="ml-2">
          <van-button
            size="small"
            type="warning"
            data-testid="switch-network-button"
            aria-label="切换网络"
            @click="switchNetwork"
          >
            切换网络
          </van-button>
        </div>

        <!-- 刷新余额按钮 -->
        <van-button
          size="small"
          icon="replay"
          data-testid="refresh-balance-button"
          aria-label="刷新余额"
          :loading="refreshing"
          @click="refreshBalance"
        />

        <!-- 断开连接按钮 -->
        <van-button
          size="small"
          type="danger"
          data-testid="disconnect-button"
          aria-label="断开连接"
          @click="confirmDisconnect"
        >
          断开
        </van-button>

        <!-- 更多操作 -->
        <van-popover
          v-model:show="showPopover"
          placement="bottom-end"
          :actions="walletActions"
          @select="handleAction"
        >
          <template #reference>
            <van-button
              size="small"
              icon="more"
              data-testid="wallet-menu-button"
              aria-label="钱包菜单"
            />
          </template>
        </van-popover>
      </div>
    </div>

    <!-- 连接钱包弹窗 -->
    <WalletConnect
      :show="showConnectModal"
      @close="showConnectModal = false"
      @connected="handleWalletConnected"
    />

    <!-- 钱包详情弹窗 -->
    <van-popup
      v-model:show="showDetailsModal"
      position="bottom"
      :style="{ height: '70%' }"
      round
      closeable
    >
      <div class="p-6">
        <h2 class="text-xl font-bold text-gray-800 mb-6">钱包详情</h2>

        <div class="space-y-4">
          <!-- 地址信息 -->
          <div class="bg-gray-50 rounded-lg p-4">
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm text-gray-600">钱包地址</span>
              <van-button size="mini" @click="copyAddress">复制</van-button>
            </div>
            <p class="font-mono text-sm break-all">{{ walletStore.address }}</p>
          </div>

          <!-- 余额信息 -->
          <div class="bg-gray-50 rounded-lg p-4">
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm text-gray-600">代币余额</span>
              <van-button size="mini" @click="refreshBalance">刷新</van-button>
            </div>
            <p class="text-lg font-semibold">{{ formattedBalance }} {{ tokenSymbol }}</p>
          </div>

          <!-- 网络信息 -->
          <div class="bg-gray-50 rounded-lg p-4">
            <span class="text-sm text-gray-600">当前网络</span>
            <div class="flex items-center mt-2">
              <div
                class="w-3 h-3 rounded-full mr-2"
                :class="networkStatusClass"
              ></div>
              <span class="font-medium">{{ networkName }}</span>
              <span class="text-sm text-gray-500 ml-2">({{ walletStore.chainId }})</span>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="space-y-3 pt-4">
            <van-button
              block
              type="primary"
              @click="switchNetwork"
              :disabled="walletStore.isCorrectNetwork"
            >
              {{ walletStore.isCorrectNetwork ? '网络正确' : '切换到正确网络' }}
            </van-button>

            <van-button
              block
              type="danger"
              @click="confirmDisconnect"
            >
              断开连接
            </van-button>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { showToast } from 'vant'
import { showConfirmDialog } from 'vant/es'
import { useWalletStore } from '../stores/wallet'
import { walletService } from '../services/wallet.service'
import WalletConnect from './WalletConnect.vue'
import type { WalletInfo } from '../types'

const walletStore = useWalletStore()

// 响应式状态
const showConnectModal = ref(false)
const showDetailsModal = ref(false)
const showPopover = ref(false)
const refreshing = ref(false)

// 计算属性
const formattedBalance = computed(() => {
  const balance = parseFloat(walletStore.balance)
  if (balance === 0) return '0'
  if (balance < 0.001) return '<0.001'
  return balance.toFixed(3)
})

const networkName = computed(() => {
  const chainId = walletStore.chainId
  const networks: Record<number, string> = {
    1: 'Ethereum Mainnet',
    11155111: 'Sepolia Testnet',
    31337: 'Hardhat Local'
  }
  return networks[chainId] || `Unknown (${chainId})`
})

const networkStatusClass = computed(() => {
  if (!walletStore.isConnected) return 'bg-gray-400'
  return walletStore.isCorrectNetwork ? 'bg-green-500' : 'bg-red-500'
})

const tokenSymbol = computed(() => {
  return (import.meta as any).env.VITE_TOKEN_SYMBOL || 'TOKEN'
})

// 钱包操作菜单
const walletActions = [
  { text: '钱包详情', value: 'details', icon: 'info' },
  { text: '刷新余额', value: 'refresh', icon: 'replay' },
  { text: '切换网络', value: 'network', icon: 'exchange' },
  { text: '断开连接', value: 'disconnect', icon: 'close', color: '#ee0a24' }
]

// 处理钱包连接成功
const handleWalletConnected = (walletInfo: WalletInfo) => {
  console.log('钱包连接成功:', walletInfo)
}

// 处理操作菜单选择
const handleAction = (action: any) => {
  showPopover.value = false

  switch (action.value) {
    case 'details':
      showDetailsModal.value = true
      break
    case 'refresh':
      refreshBalance()
      break
    case 'network':
      switchNetwork()
      break
    case 'disconnect':
      confirmDisconnect()
      break
  }
}

// 复制地址
const copyAddress = async () => {
  try {
    await navigator.clipboard.writeText(walletStore.address)
    showToast({
      type: 'success',
      message: '地址已复制到剪贴板'
    })
  } catch (error) {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = walletStore.address
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)

    showToast({
      type: 'success',
      message: '地址已复制到剪贴板'
    })
  }
}

// 刷新余额
const refreshBalance = async () => {
  if (refreshing.value || !walletStore.address) return

  refreshing.value = true
  try {
    const balance = await walletService.getTokenBalance(walletStore.address)
    walletStore.setWalletInfo({ balance })
    showToast({
      type: 'success',
      message: '余额已刷新'
    })
  } catch (error: any) {
    showToast({
      type: 'fail',
      message: '刷新失败：' + (error.message || '未知错误')
    })
  } finally {
    refreshing.value = false
  }
}

// 切换网络
const switchNetwork = async () => {
  try {
    const targetChainId = parseInt((import.meta as any).env.VITE_CHAIN_ID || '31337')
    await walletService.switchNetwork(targetChainId)
    showToast({
      type: 'success',
      message: '网络切换成功'
    })
  } catch (error: any) {
    showToast({
      type: 'fail',
      message: '网络切换失败：' + (error.message || '未知错误')
    })
  }
}

// 确认断开连接
const confirmDisconnect = async () => {
  try {
    await showConfirmDialog({
      title: '断开连接',
      message: '确定要断开钱包连接吗？',
      confirmButtonText: '断开',
      cancelButtonText: '取消'
    })
    disconnect()
  } catch (error) {
    // 用户取消了操作
    console.log('用户取消断开连接')
  }
}

// 断开连接
const disconnect = () => {
  walletService.disconnect()
  walletStore.disconnect()

  showToast({
    type: 'success',
    message: '钱包已断开连接'
  })
}

// 监听钱包事件
const handleWalletDisconnected = () => {
  walletStore.disconnect()
  localStorage.removeItem('wallet_connected')
  localStorage.removeItem('wallet_type')
}

const handleAccountChanged = (event: CustomEvent) => {
  const { address, balance } = event.detail
  walletStore.setWalletInfo({ address, balance })
  showToast({
    type: 'success',
    message: '账户已切换'
  })
}

const handleNetworkChanged = (event: CustomEvent) => {
  const { chainId } = event.detail
  walletStore.setWalletInfo({ chainId })

  if (!walletStore.isCorrectNetwork) {
    showToast({
      type: 'fail',
      message: '请切换到正确的网络'
    })
  }
}

// 自动重连
const autoReconnect = async () => {
  const wasConnected = localStorage.getItem('wallet_connected') === 'true'
  const walletType = walletStore.walletType || localStorage.getItem('wallet_type') as 'metamask' | 'walletconnect' | null

  if (!wasConnected || !walletType) return

  try {
    let walletInfo: WalletInfo | null = null

    if (walletType === 'metamask' && walletService.isMetaMaskInstalled()) {
      // 检查MetaMask是否已经连接
      if (typeof window.ethereum !== 'undefined' && (window.ethereum as any).selectedAddress) {
        walletInfo = await walletService.connectMetaMask()
      }
    } else if (walletType === 'walletconnect') {
      // 尝试重新连接WalletConnect
      try {
        walletInfo = await walletService.connectWalletConnect()
      } catch (error) {
        console.log('WalletConnect自动重连失败:', error)
      }
    }

    if (walletInfo) {
      walletStore.setWalletInfo(walletInfo)
      walletStore.setWalletType(walletType)
      console.log('钱包自动重连成功:', walletInfo.address)
    }
  } catch (error) {
    console.log('自动重连失败:', error)
    // 清除无效的连接状态
    walletStore.disconnect()
  }
}

// 生命周期
onMounted(() => {
  // 监听钱包事件
  window.addEventListener('wallet-disconnected', handleWalletDisconnected)
  window.addEventListener('wallet-account-changed', handleAccountChanged as EventListener)
  window.addEventListener('wallet-network-changed', handleNetworkChanged as EventListener)

  // 自动重连
  autoReconnect()
})

onUnmounted(() => {
  // 清理事件监听器
  window.removeEventListener('wallet-disconnected', handleWalletDisconnected)
  window.removeEventListener('wallet-account-changed', handleAccountChanged as EventListener)
  window.removeEventListener('wallet-network-changed', handleNetworkChanged as EventListener)
})
</script>

<style scoped>
.wallet-status {
  @apply flex items-center;
}

.wallet-connected {
  @apply flex items-center;
}

.wallet-disconnected {
  @apply flex items-center;
}
</style>
