<template>
  <van-popup
    v-model:show="showModal"
    position="bottom"
    :style="{ height: '60%' }"
    round
    closeable
    close-icon="cross"
    @close="$emit('close')"
    data-testid="wallet-connect-modal"
    role="dialog"
    :aria-labelledby="modalTitleId"
    aria-describedby="modal-description"
  >
    <div class="p-6">
      <div class="text-center mb-6">
        <h2 :id="modalTitleId" class="text-xl font-bold text-gray-800 mb-2">连接钱包</h2>
        <p id="modal-description" class="text-gray-600 text-sm">选择一个钱包来连接到萌宠养成游戏</p>
      </div>

      <!-- 钱包选项列表 -->
      <div class="space-y-4">
        <!-- MetaMask -->
        <button
          data-testid="metamask-option"
          class="w-full flex items-center p-4 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500"
          :class="{ 'opacity-50 cursor-not-allowed': !isMetaMaskInstalled }"
          :disabled="!isMetaMaskInstalled || connecting === 'metamask'"
          :aria-label="isMetaMaskInstalled ? '连接MetaMask钱包' : '安装MetaMask'"
          @click="connectMetaMask"
          @keydown.enter="connectMetaMask"
          @keydown.space.prevent="connectMetaMask"
        >
          <div class="w-12 h-12 mr-4 flex items-center justify-center">
            <img
              src="/images/wallets/metamask.svg"
              alt="MetaMask"
              class="w-10 h-10"
              @error="handleImageError"
            />
          </div>
          <div class="flex-1 text-left">
            <h3 class="font-semibold text-gray-800">MetaMask</h3>
            <p class="text-sm text-gray-600">
              {{ isMetaMaskInstalled ? '连接到MetaMask钱包' : '请先安装MetaMask' }}
            </p>
          </div>
          <div v-if="connecting === 'metamask'" class="ml-4" data-testid="loading-spinner">
            <van-loading size="20px" />
          </div>
          <div v-else-if="!isMetaMaskInstalled" class="ml-4">
            <van-button
              size="small"
              type="primary"
              aria-label="安装MetaMask"
              @click.stop="installMetaMask"
            >
              安装
            </van-button>
          </div>
        </button>

        <!-- WalletConnect -->
        <button
          data-testid="walletconnect-option"
          class="w-full flex items-center p-4 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500"
          :disabled="connecting === 'walletconnect'"
          aria-label="使用WalletConnect连接钱包"
          @click="connectWalletConnect"
          @keydown.enter="connectWalletConnect"
          @keydown.space.prevent="connectWalletConnect"
        >
          <div class="w-12 h-12 mr-4 flex items-center justify-center">
            <img
              src="/images/wallets/walletconnect.svg"
              alt="WalletConnect"
              class="w-10 h-10"
              @error="handleImageError"
            />
          </div>
          <div class="flex-1 text-left">
            <h3 class="font-semibold text-gray-800">WalletConnect</h3>
            <p class="text-sm text-gray-600">使用WalletConnect协议连接</p>
          </div>
          <div v-if="connecting === 'walletconnect'" class="ml-4" data-testid="loading-spinner">
            <van-loading size="20px" />
          </div>
        </button>
      </div>

      <!-- 网络状态提示 -->
      <div v-if="networkError" class="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg" data-testid="network-error">
        <div class="flex items-center">
          <van-icon name="warning" class="text-red-500 mr-2" />
          <div>
            <p class="text-red-800 font-medium">网络错误</p>
            <p class="text-red-600 text-sm">{{ networkError }}</p>
          </div>
        </div>
        <van-button
          class="mt-3"
          size="small"
          type="danger"
          aria-label="切换到正确网络"
          @click="switchToCorrectNetwork"
        >
          切换网络
        </van-button>
      </div>

      <!-- 连接错误提示 -->
      <div v-if="connectionError" class="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg" data-testid="error-message">
        <div class="flex items-center">
          <van-icon name="warning" class="text-red-500 mr-2" />
          <div>
            <p class="text-red-800 font-medium">连接失败</p>
            <p class="text-red-600 text-sm">{{ connectionError }}</p>
          </div>
        </div>
      </div>

      <!-- 帮助信息 -->
      <div class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <div class="flex items-start">
          <van-icon name="info" class="text-blue-500 mr-2 mt-0.5" />
          <div>
            <p class="text-blue-800 font-medium text-sm">新手提示</p>
            <p class="text-blue-600 text-xs mt-1">
              首次连接需要在钱包中确认授权。连接后您可以开始萌宠养成游戏并获得代币奖励。
            </p>
          </div>
        </div>
      </div>
    </div>
  </van-popup>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { showToast } from 'vant'
import { walletService } from '../services/wallet.service'
import { useWallet } from '../composables/useWallet'
import type { WalletInfo } from '../types'

// 定义组件名称
defineOptions({
  name: 'WalletConnect'
})

interface Props {
  show: boolean
}

interface Emits {
  (e: 'close'): void
  (e: 'connected', walletInfo: WalletInfo): void
  (e: 'connect-wallet', type: 'metamask' | 'walletconnect'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const {
  connectMetaMask: connectMetaMaskWallet,
  connectWalletConnect: connectWalletConnectWallet,
  isConnecting,
  connectionError,
  networkError
} = useWallet()

// 响应式状态
const connecting = ref<'metamask' | 'walletconnect' | null>(null)

// 生成唯一ID用于可访问性
const modalTitleId = `wallet-modal-title-${Math.random().toString(36).substr(2, 9)}`

// 计算属性
const showModal = computed({
  get: () => props.show,
  set: (value) => {
    if (!value) emit('close')
  }
})

const isMetaMaskInstalled = computed(() => {
  return walletService.isMetaMaskInstalled()
})

// 连接MetaMask
const connectMetaMask = async () => {
  if (!isMetaMaskInstalled.value) {
    installMetaMask()
    return
  }

  if (connecting.value) return

  connecting.value = 'metamask'
  emit('connect-wallet', 'metamask')

  try {
    await connectMetaMaskWallet()
    emit('connected', { isConnected: true } as WalletInfo)
    emit('close')
  } catch (error: any) {
    console.error('连接MetaMask失败:', error)
  } finally {
    connecting.value = null
  }
}

// 连接WalletConnect
const connectWalletConnect = async () => {
  if (connecting.value) return

  connecting.value = 'walletconnect'
  emit('connect-wallet', 'walletconnect')

  try {
    await connectWalletConnectWallet()
    emit('connected', { isConnected: true } as WalletInfo)
    emit('close')
  } catch (error: any) {
    console.error('连接WalletConnect失败:', error)
  } finally {
    connecting.value = null
  }
}

// 安装MetaMask
const installMetaMask = () => {
  window.open('https://metamask.io/download/', '_blank')
}

// 切换到正确网络
const switchToCorrectNetwork = async () => {
  try {
    const targetChainId = parseInt((import.meta as any).env.VITE_CHAIN_ID || '31337')
    await walletService.switchNetwork(targetChainId)
    networkError.value = ''
    showToast({
      type: 'success',
      message: '网络切换成功'
    })
  } catch (error: any) {
    showToast({
      type: 'fail',
      message: '网络切换失败：' + (error.message || '未知错误')
    })
  }
}

// 处理图片加载错误
const handleImageError = (event: Event) => {
  const target = event.target as HTMLImageElement
  target.style.display = 'none'

  // 创建一个简单的文字替代
  const parent = target.parentElement
  if (parent && !parent.querySelector('.wallet-icon-fallback')) {
    const fallback = document.createElement('div')
    fallback.className = 'wallet-icon-fallback w-10 h-10 bg-gray-200 rounded-lg flex items-center justify-center text-gray-600 text-xs font-bold'
    fallback.textContent = target.alt?.charAt(0) || 'W'
    parent.appendChild(fallback)
  }
}
</script>

<style scoped>
.wallet-icon-fallback {
  min-width: 40px;
  min-height: 40px;
}
</style>
