import { ethers } from 'ethers'
import { CONTRACT_CONFIG } from '../config/constants'
import { contractService } from './contract.service'
import { walletPersistenceService } from './wallet-persistence.service'
import { securityAuditService } from './security-audit.service'
import { InputValidator } from '../utils/validation'
import type { WalletInfo, EthereumProvider, NetworkConfig } from '../types'

export class WalletService {
  private provider: ethers.BrowserProvider | null = null
  private signer: ethers.Signer | null = null
  private walletConnectProvider: any = null

  /**
   * 检查是否安装了MetaMask
   * @returns 是否安装了MetaMask
   */
  isMetaMaskInstalled(): boolean {
    return typeof window !== 'undefined' && typeof window.ethereum !== 'undefined'
  }

  /**
   * 连接MetaMask钱包
   * @returns 钱包信息
   */
  async connectMetaMask(): Promise<WalletInfo> {
    if (!this.isMetaMaskInstalled()) {
      securityAuditService.logSecurityEvent({
        type: 'wallet_connection',
        severity: 'medium',
        description: 'MetaMask未安装',
        details: { userAgent: navigator.userAgent }
      })
      throw new Error('请安装MetaMask')
    }

    try {
      // 请求用户授权
      await (window.ethereum as any).request({ method: 'eth_requestAccounts' })

      // 创建provider和signer
      this.provider = new ethers.BrowserProvider(window.ethereum as any)
      this.signer = await this.provider.getSigner()

      // 获取账户信息
      const address = await this.signer.getAddress()

      // 验证地址格式
      if (!InputValidator.validateAddress(address)) {
        throw new Error('获取到无效的钱包地址')
      }

      const chainId = Number((await this.provider.getNetwork()).chainId)

      // 验证网络
      if (!InputValidator.validateChainId(chainId)) {
        throw new Error('无效的网络ID')
      }

      const balance = await this.getTokenBalance(address)

      // 初始化合约服务
      await contractService.initialize(this.provider, this.signer)

      // 监听账户变化
      this.setupAccountChangeListener()
      // 监听网络变化
      this.setupNetworkChangeListener()

      const walletInfo = {
        isConnected: true,
        address,
        balance,
        chainId,
        provider: this.provider
      }

      // 记录安全事件
      securityAuditService.logSecurityEvent({
        type: 'wallet_connection',
        severity: 'low',
        description: 'MetaMask连接成功',
        details: {
          address: address.slice(0, 6) + '...' + address.slice(-4),
          chainId,
          walletType: 'metamask'
        },
        userAddress: address
      })

      // 保存钱包状态
      walletPersistenceService.saveWalletState(walletInfo, 'metamask')

      return walletInfo
    } catch (error) {
      securityAuditService.logSecurityEvent({
        type: 'wallet_connection',
        severity: 'high',
        description: 'MetaMask连接失败',
        details: {
          error: error instanceof Error ? error.message : String(error),
          walletType: 'metamask'
        }
      })
      console.error('连接MetaMask失败:', error)
      throw new Error('连接钱包失败')
    }
  }

  /**
   * 连接WalletConnect
   * @returns 钱包信息
   */
  async connectWalletConnect(): Promise<WalletInfo> {
    try {
      // 动态导入WalletConnect，避免在不需要时加载
      const { EthereumProvider } = await import('@walletconnect/ethereum-provider')

      // 创建WalletConnect提供者
      this.walletConnectProvider = await EthereumProvider.init({
        projectId: process.env.VITE_WALLETCONNECT_PROJECT_ID || 'c4f79cc821944d9680842e34466bfbd',
        chains: [CONTRACT_CONFIG.CHAIN_ID],
        optionalChains: [1, ********, 31337, 1337], // 支持的链
        showQrModal: true,
        rpcMap: {
          [CONTRACT_CONFIG.CHAIN_ID]: CONTRACT_CONFIG.RPC_URL,
          1: 'https://mainnet.infura.io/v3/your-infura-key',
          ********: 'https://sepolia.infura.io/v3/your-infura-key'
        },
        metadata: {
          name: '萌宠养成代币游戏',
          description: '基于区块链的萌宠养成游戏',
          url: window.location.origin,
          icons: [`${window.location.origin}/favicon.ico`]
        }
      })

      // 连接钱包
      await this.walletConnectProvider.connect()

      // 创建ethers提供者
      this.provider = new ethers.BrowserProvider(this.walletConnectProvider as any)
      this.signer = await this.provider.getSigner()

      // 获取账户信息
      const address = await this.signer.getAddress()
      const chainId = Number((await this.provider.getNetwork()).chainId)
      const balance = await this.getTokenBalance(address)

      // 初始化合约服务
      await contractService.initialize(this.provider, this.signer)

      // 监听WalletConnect事件
      this.walletConnectProvider.on('accountsChanged', (accounts: string[]) => {
        if (accounts.length === 0) {
          this.handleDisconnect()
        } else {
          this.handleAccountsChanged(accounts)
        }
      })

      this.walletConnectProvider.on('chainChanged', (chainId: number) => {
        this.handleChainChanged(chainId)
      })

      this.walletConnectProvider.on('disconnect', () => {
        this.handleDisconnect()
      })

      const walletInfo = {
        isConnected: true,
        address,
        balance,
        chainId,
        provider: this.provider
      }

      // 保存钱包状态
      walletPersistenceService.saveWalletState(walletInfo, 'walletconnect')

      return walletInfo
    } catch (error) {
      console.error('连接WalletConnect失败:', error)
      throw new Error('连接WalletConnect失败')
    }
  }

  /**
   * 断开钱包连接
   */
  disconnect(): void {
    const currentAddress = this.signer ? 'unknown' : 'none'

    // 如果是WalletConnect，需要断开连接
    if (this.walletConnectProvider) {
      try {
        this.walletConnectProvider.disconnect()
      } catch (error) {
        console.error('断开WalletConnect失败:', error)
      }
      this.walletConnectProvider = null
    }

    this.provider = null
    this.signer = null
    contractService.cleanup()

    // 清除持久化状态
    walletPersistenceService.clearWalletState()

    // 记录安全事件
    securityAuditService.logSecurityEvent({
      type: 'wallet_connection',
      severity: 'low',
      description: '钱包断开连接',
      details: {
        previousAddress: currentAddress,
        disconnectTime: new Date().toISOString()
      }
    })

    // 触发断开连接事件
    this.handleDisconnect()
  }

  /**
   * 检查钱包连接状态
   */
  async checkConnectionStatus(): Promise<boolean> {
    try {
      if (this.isMetaMaskInstalled() && window.ethereum) {
        const accounts = await (window.ethereum as any).request({
          method: 'eth_accounts'
        })
        return accounts && accounts.length > 0
      }
      return false
    } catch (error) {
      console.error('检查连接状态失败:', error)
      return false
    }
  }

  /**
   * 尝试自动重连
   */
  async tryAutoReconnect(): Promise<WalletInfo | null> {
    const persistedData = walletPersistenceService.loadWalletState()
    if (!persistedData) return null

    try {
      let walletInfo: WalletInfo | null = null

      if (persistedData.walletType === 'metamask' && this.isMetaMaskInstalled()) {
        // 检查MetaMask是否仍然连接
        const isConnected = await this.checkConnectionStatus()
        if (isConnected) {
          walletInfo = await this.connectMetaMask()
        }
      } else if (persistedData.walletType === 'walletconnect') {
        // WalletConnect需要用户手动重新连接
        console.log('WalletConnect需要手动重新连接')
      }

      if (walletInfo) {
        securityAuditService.logSecurityEvent({
          type: 'wallet_connection',
          severity: 'low',
          description: '钱包自动重连成功',
          details: {
            walletType: persistedData.walletType,
            address: walletInfo.address.slice(0, 6) + '...' + walletInfo.address.slice(-4)
          },
          userAddress: walletInfo.address
        })
      }

      return walletInfo
    } catch (error) {
      console.error('自动重连失败:', error)
      walletPersistenceService.clearWalletState()
      return null
    }
  }

  /**
   * 处理账户变化
   * @param accounts 账户列表
   */
  private async handleAccountsChanged(accounts: string[]): Promise<void> {
    if (accounts.length === 0) {
      this.handleDisconnect()
      return
    }

    try {
      if (this.provider) {
        this.signer = await this.provider.getSigner()
        await contractService.updateSigner(this.signer)

        const address = accounts[0]
        const balance = await this.getTokenBalance(address)

        window.dispatchEvent(new CustomEvent('wallet-account-changed', {
          detail: { address, balance }
        }))
      }
    } catch (error) {
      console.error('处理账户变化失败:', error)
    }
  }

  /**
   * 处理链变化
   * @param chainId 链ID
   */
  private handleChainChanged(chainId: number | string): void {
    // 确保chainId是数字
    const numericChainId = typeof chainId === 'string'
      ? parseInt(chainId, 16)
      : chainId

    window.dispatchEvent(new CustomEvent('wallet-network-changed', {
      detail: { chainId: numericChainId }
    }))

    // 刷新页面以确保所有状态正确更新
    window.location.reload()
  }

  /**
   * 处理断开连接
   */
  private handleDisconnect(): void {
    window.dispatchEvent(new CustomEvent('wallet-disconnected'))
  }

  /**
   * 获取代币余额
   * @param address 地址
   * @returns 余额
   */
  async getTokenBalance(address: string): Promise<string> {
    try {
      return await contractService.getBalance(address)
    } catch (error) {
      console.error('获取代币余额失败:', error)
      return '0'
    }
  }

  /**
   * 获取ETH余额
   * @param address 地址
   * @returns ETH余额
   */
  async getEthBalance(address: string): Promise<string> {
    if (!this.provider) {
      throw new Error('未连接钱包')
    }

    try {
      const balance = await this.provider.getBalance(address)
      return balance.toString()
    } catch (error) {
      console.error('获取ETH余额失败:', error)
      throw new Error('获取ETH余额失败')
    }
  }

  /**
   * 切换网络
   * @param chainId 目标链ID
   */
  async switchNetwork(chainId: number): Promise<void> {
    if (!window.ethereum) {
      throw new Error('请安装MetaMask')
    }

    const chainIdHex = '0x' + chainId.toString(16)

    try {
      await (window.ethereum as any).request({
        method: 'wallet_switchEthereumChain',
        params: [{ chainId: chainIdHex }]
      })

      // 记录网络切换事件
      securityAuditService.logSecurityEvent({
        type: 'network_switch',
        severity: 'low',
        description: '网络切换成功',
        details: {
          targetChainId: chainId,
          chainIdHex
        }
      })
    } catch (error: any) {
      // 如果网络不存在，尝试添加网络
      if (error.code === 4902) {
        await this.addNetwork(chainId)
      } else {
        securityAuditService.logSecurityEvent({
          type: 'network_switch',
          severity: 'medium',
          description: '网络切换失败',
          details: {
            targetChainId: chainId,
            error: error.message || String(error)
          }
        })
        console.error('切换网络失败:', error)
        throw new Error('切换网络失败')
      }
    }
  }

  /**
   * 获取网络配置
   * @param chainId 链ID
   * @returns 网络配置
   */
  private getNetworkConfig(chainId: number): NetworkConfig | null {
    const networks: Record<number, NetworkConfig> = {
      1: {
        chainId: 1,
        chainName: 'Ethereum Mainnet',
        nativeCurrency: {
          name: 'Ethereum',
          symbol: 'ETH',
          decimals: 18
        },
        rpcUrls: ['https://mainnet.infura.io/v3/YOUR_INFURA_KEY'],
        blockExplorerUrls: ['https://etherscan.io']
      },
      ********: {
        chainId: ********,
        chainName: 'Sepolia Testnet',
        nativeCurrency: {
          name: 'Ethereum',
          symbol: 'ETH',
          decimals: 18
        },
        rpcUrls: ['https://rpc.sepolia.org'],
        blockExplorerUrls: ['https://sepolia.etherscan.io']
      },
      31337: {
        chainId: 31337,
        chainName: 'Hardhat Local',
        nativeCurrency: {
          name: 'Ethereum',
          symbol: 'ETH',
          decimals: 18
        },
        rpcUrls: [CONTRACT_CONFIG.RPC_URL || 'http://127.0.0.1:8545'],
        blockExplorerUrls: []
      },
      1337: {
        chainId: 1337,
        chainName: 'Ganache Local',
        nativeCurrency: {
          name: 'Ethereum',
          symbol: 'ETH',
          decimals: 18
        },
        rpcUrls: ['http://127.0.0.1:7545'],
        blockExplorerUrls: []
      }
    }

    return networks[chainId] || null
  }

  /**
   * 添加网络
   * @param chainId 链ID
   */
  private async addNetwork(chainId: number): Promise<void> {
    if (!window.ethereum) {
      throw new Error('请安装MetaMask')
    }

    const networkConfig = this.getNetworkConfig(chainId)
    if (!networkConfig) {
      throw new Error('不支持的网络')
    }

    const chainIdHex = '0x' + chainId.toString(16)

    try {
      await (window.ethereum as any).request({
        method: 'wallet_addEthereumChain',
        params: [{
          chainId: chainIdHex,
          chainName: networkConfig.chainName,
          nativeCurrency: networkConfig.nativeCurrency,
          rpcUrls: networkConfig.rpcUrls,
          blockExplorerUrls: networkConfig.blockExplorerUrls
        }]
      })
    } catch (error) {
      console.error('添加网络失败:', error)
      throw new Error('添加网络失败')
    }
  }

  /**
   * 获取支持的网络列表
   * @returns 支持的网络列表
   */
  getSupportedNetworks(): NetworkConfig[] {
    return [
      this.getNetworkConfig(31337),
      this.getNetworkConfig(********),
      this.getNetworkConfig(1)
    ].filter(Boolean) as NetworkConfig[]
  }

  /**
   * 检查是否为支持的网络
   * @param chainId 链ID
   * @returns 是否支持
   */
  isSupportedNetwork(chainId: number): boolean {
    return this.getNetworkConfig(chainId) !== null
  }

  /**
   * 设置账户变化监听器
   */
  private setupAccountChangeListener(): void {
    if (!window.ethereum) return

    const ethereum = window.ethereum as EthereumProvider
    ethereum.on('accountsChanged', async (accounts: string[]) => {
      await this.handleAccountsChanged(accounts)
    })
  }

  /**
   * 设置网络变化监听器
   */
  private setupNetworkChangeListener(): void {
    if (!window.ethereum) return

    const ethereum = window.ethereum as EthereumProvider
    ethereum.on('chainChanged', async (chainIdHex: string) => {
      const chainId = parseInt(chainIdHex, 16)

      window.dispatchEvent(new CustomEvent('wallet-network-changed', {
        detail: { chainId }
      }))

      // 刷新页面以确保所有状态正确更新
      window.location.reload()
    })
  }
}

// 导出单例实例
export const walletService = new WalletService()
