import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useWalletStore } from '../stores/wallet'
import { walletService } from '../services/wallet.service'
import { showToast } from 'vant'
import { showConfirmDialog } from 'vant/es'
import type { WalletInfo } from '../types'

export function useWallet() {
  const walletStore = useWalletStore()

  // 响应式状态
  const isConnecting = ref(false)
  const connectionError = ref('')
  const networkError = ref('')
  const showConnectModal = ref(false)

  // 计算属性
  const isConnected = computed(() => walletStore.isConnected)
  const address = computed(() => walletStore.address)
  const shortAddress = computed(() => walletStore.shortAddress)
  const balance = computed(() => walletStore.formattedBalance)
  const chainId = computed(() => walletStore.chainId)
  const isCorrectNetwork = computed(() => walletStore.isCorrectNetwork)
  const networkName = computed(() => walletStore.networkName)

  // 连接MetaMask
  const connectMetaMask = async () => {
    if (!walletService.isMetaMaskInstalled()) {
      showToast({
        type: 'fail',
        message: '请先安装MetaMask钱包'
      })
      return
    }

    if (isConnecting.value) return

    isConnecting.value = true
    connectionError.value = ''
    networkError.value = ''

    try {
      walletStore.setConnecting(true)
      const walletInfo = await walletService.connectMetaMask()

      // 检查网络
      const targetChainId = parseInt((import.meta as any).env.VITE_CHAIN_ID || '31337')
      if (walletInfo.chainId !== targetChainId) {
        networkError.value = `请切换到正确的网络 (Chain ID: ${targetChainId})`

        // 询问是否自动切换网络
        let shouldSwitch = false
        try {
          await showConfirmDialog({
            title: '网络不匹配',
            message: `当前网络不正确，是否切换到目标网络？`,
            confirmButtonText: '切换',
            cancelButtonText: '取消'
          })
          shouldSwitch = true
        } catch (error) {
          shouldSwitch = false
        }

        if (shouldSwitch) {
          await switchNetwork(targetChainId)
          // 重新获取钱包信息
          const updatedWalletInfo = await walletService.connectMetaMask()
          walletStore.setWalletInfo(updatedWalletInfo)
        } else {
          return
        }
      } else {
        walletStore.setWalletInfo(walletInfo)
      }

      walletStore.setWalletType('metamask')
      localStorage.setItem('wallet_connected', 'true')

      showToast({
        type: 'success',
        message: '钱包连接成功！'
      })

      showConnectModal.value = false
    } catch (error: any) {
      console.error('连接MetaMask失败:', error)
      connectionError.value = error.message || '连接失败，请重试'
      showToast({
        type: 'fail',
        message: '连接失败：' + (error.message || '未知错误')
      })
    } finally {
      isConnecting.value = false
      walletStore.setConnecting(false)
    }
  }

  // 连接WalletConnect
  const connectWalletConnect = async () => {
    if (isConnecting.value) return

    isConnecting.value = true
    connectionError.value = ''
    networkError.value = ''

    try {
      walletStore.setConnecting(true)
      const walletInfo = await walletService.connectWalletConnect()

      // 检查网络
      const targetChainId = parseInt((import.meta as any).env.VITE_CHAIN_ID || '31337')
      if (walletInfo.chainId !== targetChainId) {
        networkError.value = `请切换到正确的网络 (Chain ID: ${targetChainId})`

        // WalletConnect通常需要在钱包应用中手动切换网络
        showToast({
          type: 'fail',
          message: '请在您的钱包应用中切换到正确的网络'
        })
        return
      }

      walletStore.setWalletInfo(walletInfo)
      walletStore.setWalletType('walletconnect')
      localStorage.setItem('wallet_connected', 'true')

      showToast({
        type: 'success',
        message: '钱包连接成功！'
      })

      showConnectModal.value = false
    } catch (error: any) {
      console.error('连接WalletConnect失败:', error)
      connectionError.value = error.message || '连接失败，请重试'
      showToast({
        type: 'fail',
        message: '连接失败：' + (error.message || '未知错误')
      })
    } finally {
      isConnecting.value = false
      walletStore.setConnecting(false)
    }
  }

  // 断开连接
  const disconnect = async () => {
    try {
      await showConfirmDialog({
        title: '断开连接',
        message: '确定要断开钱包连接吗？',
        confirmButtonText: '断开',
        cancelButtonText: '取消'
      })

      walletService.disconnect()
      walletStore.disconnect()

      showToast({
        type: 'success',
        message: '钱包已断开连接'
      })
    } catch (error) {
      // 用户取消了操作
      console.log('用户取消断开连接')
    }
  }

  // 切换网络
  const switchNetwork = async (targetChainId?: number) => {
    try {
      const chainId = targetChainId || parseInt((import.meta as any).env.VITE_CHAIN_ID || '31337')
      await walletService.switchNetwork(chainId)
      networkError.value = ''
      showToast({
        type: 'success',
        message: '网络切换成功'
      })
    } catch (error: any) {
      showToast({
        type: 'fail',
        message: '网络切换失败：' + (error.message || '未知错误')
      })
    }
  }

  // 刷新余额
  const refreshBalance = async () => {
    if (!walletStore.address) return

    try {
      const balance = await walletService.getTokenBalance(walletStore.address)
      walletStore.setWalletInfo({ balance })
      showToast({
        type: 'success',
        message: '余额已刷新'
      })
    } catch (error: any) {
      showToast({
        type: 'fail',
        message: '刷新失败：' + (error.message || '未知错误')
      })
    }
  }

  // 复制地址
  const copyAddress = async () => {
    if (!walletStore.address) return

    try {
      await navigator.clipboard.writeText(walletStore.address)
      showToast({
        type: 'success',
        message: '地址已复制到剪贴板'
      })
    } catch (error) {
      // 降级方案
      const textArea = document.createElement('textarea')
      textArea.value = walletStore.address
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)

      showToast({
        type: 'success',
        message: '地址已复制到剪贴板'
      })
    }
  }

  // 自动重连
  const autoReconnect = async () => {
    try {
      const walletInfo = await walletService.tryAutoReconnect()
      if (walletInfo) {
        walletStore.setWalletInfo(walletInfo)
        const walletType = walletStore.walletType || localStorage.getItem('wallet_type') as 'metamask' | 'walletconnect' | null
        if (walletType) {
          walletStore.setWalletType(walletType)
        }
        console.log('钱包自动重连成功:', walletInfo.address)
      }
    } catch (error) {
      console.log('自动重连失败:', error)
      // 清除无效的连接状态
      walletStore.disconnect()
    }
  }

  // 检查网络兼容性
  const checkNetworkCompatibility = () => {
    if (!walletStore.isConnected) return true

    const targetChainId = parseInt((import.meta as any).env.VITE_CHAIN_ID || '31337')
    if (walletStore.chainId !== targetChainId) {
      networkError.value = `请切换到正确的网络 (Chain ID: ${targetChainId})`
      return false
    }

    networkError.value = ''
    return true
  }

  // 验证钱包状态
  const validateWalletState = () => {
    if (!walletStore.isConnected) return false

    const isValidAddress = walletStore.address && walletStore.address.length === 42 && walletStore.address.startsWith('0x')
    const isValidChainId = walletStore.chainId > 0
    const isValidBalance = walletStore.balance !== undefined

    return isValidAddress && isValidChainId && isValidBalance
  }

  // 事件处理器
  const handleWalletDisconnected = () => {
    walletStore.disconnect()
    localStorage.removeItem('wallet_connected')
    localStorage.removeItem('wallet_type')
  }

  const handleAccountChanged = (event: CustomEvent) => {
    const { address, balance } = event.detail
    walletStore.setWalletInfo({ address, balance })
    showToast({
      type: 'success',
      message: '账户已切换'
    })
  }

  const handleNetworkChanged = (event: CustomEvent) => {
    const { chainId } = event.detail
    walletStore.setWalletInfo({ chainId })

    if (!walletStore.isCorrectNetwork) {
      showToast({
        type: 'fail',
        message: '请切换到正确的网络'
      })
    }
  }

  // 生命周期
  onMounted(() => {
    // 监听钱包事件
    window.addEventListener('wallet-disconnected', handleWalletDisconnected)
    window.addEventListener('wallet-account-changed', handleAccountChanged as EventListener)
    window.addEventListener('wallet-network-changed', handleNetworkChanged as EventListener)

    // 自动重连
    autoReconnect()
  })

  onUnmounted(() => {
    // 清理事件监听器
    window.removeEventListener('wallet-disconnected', handleWalletDisconnected)
    window.removeEventListener('wallet-account-changed', handleAccountChanged as EventListener)
    window.removeEventListener('wallet-network-changed', handleNetworkChanged as EventListener)
  })

  return {
    // 状态
    isConnected,
    address,
    shortAddress,
    balance,
    chainId,
    isCorrectNetwork,
    networkName,
    isConnecting,
    connectionError,
    networkError,
    showConnectModal,

    // 方法
    connectMetaMask,
    connectWalletConnect,
    disconnect,
    switchNetwork,
    refreshBalance,
    copyAddress,
    autoReconnect,
    checkNetworkCompatibility,
    validateWalletState
  }
}
