<template>
  <AppLayoutWithSidebar>
    <!-- 页面头部 -->
    <section class="page-header">
      <div class="container">
        <h1 class="page-title">游戏教程</h1>
        <p class="page-desc">学习如何玩转萌宠养成游戏</p>
        <div class="header-actions">
          <button class="icon-btn" @click="handleBack" aria-label="返回">
            <van-icon name="arrow-left" />
          </button>
          <span class="step-indicator">{{ currentStepIndex + 1 }}/{{ tutorialSteps.length }}</span>
        </div>
      </div>
    </section>

    <!-- 进度条 -->
    <div class="progress-container">
      <div class="container">
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: `${progress}%` }"></div>
        </div>
      </div>
    </div>

    <!-- 教程内容 -->
    <section class="tutorial-content">
      <div class="container">
        <transition name="fade" mode="out-in">
          <div :key="currentStepIndex" class="step-content">
            <!-- 步骤图标/插图 -->
            <div class="step-illustration">
              <component :is="currentStep.icon" class="step-icon" />
            </div>

            <!-- 步骤描述 -->
            <div class="step-description">
              <p v-for="(paragraph, index) in currentStep.description" :key="index" class="description-paragraph">
                {{ paragraph }}
              </p>
            </div>

            <!-- 交互式演示 -->
            <div v-if="currentStep.interactive" class="interactive-demo">
              <h3 class="demo-title">互动演示</h3>
              <component :is="currentStep.demoComponent" @demo-complete="handleDemoComplete" />
            </div>

            <!-- 关键点提示 -->
            <div v-if="currentStep.keyPoints" class="key-points">
              <h3 class="key-points-title">重要提示</h3>
              <ul class="key-points-list">
                <li v-for="(point, index) in currentStep.keyPoints" :key="index" class="key-point">
                  <span class="point-icon">•</span>
                  <span>{{ point }}</span>
                </li>
              </ul>
            </div>
          </div>
        </transition>
      </div>
    </section>

    <!-- 底部操作按钮 -->
    <section class="tutorial-actions">
      <div class="container">
        <div class="actions-wrapper">
          <button
            v-if="currentStepIndex > 0"
            class="secondary-btn"
            @click="previousStep"
          >
            上一步
          </button>

          <button
            v-if="!isLastStep"
            class="primary-btn"
            @click="nextStep"
            :disabled="currentStep.interactive && !demoCompleted"
          >
            {{ currentStep.interactive && !demoCompleted ? '完成演示继续' : '下一步' }}
          </button>

          <button
            v-if="isLastStep"
            class="primary-btn complete-btn"
            @click="completeTutorial"
          >
            开始游戏
          </button>
        </div>

        <!-- 跳过教程按钮 -->
        <div class="skip-tutorial">
          <button
            class="text-btn"
            @click="showSkipDialog"
          >
            跳过教程
          </button>
        </div>
      </div>
    </section>

    <!-- 侧边导航 (仅在大屏幕显示) -->
    <div class="side-navigation">
      <div class="steps-list">
        <div
          v-for="(step, index) in tutorialSteps"
          :key="step.id"
          class="step-item"
          :class="{ 'active': index === currentStepIndex, 'completed': index < currentStepIndex }"
          @click="goToStep(index)"
        >
          <div class="step-number">{{ index + 1 }}</div>
          <div class="step-label">{{ step.title }}</div>
        </div>
      </div>
    </div>
  </AppLayoutWithSidebar>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, h } from 'vue'
import { useRouter } from 'vue-router'
import { showDialog, showToast } from 'vant'
import { useGameStore } from '../stores/game'
import { usePetStore } from '../stores/pet'
import AppLayoutWithSidebar from '../components/layout/AppLayoutWithSidebar.vue'
import PetIntroDemo from '../components/tutorial/PetIntroDemo.vue'
import TokenExchangeDemo from '../components/tutorial/TokenExchangeDemo.vue'
import GameFlowDemo from '../components/tutorial/GameFlowDemo.vue'

// 图标组件
const PetIcon = () => h('div', { class: 'icon-wrapper pet-icon' }, '🐱')
const TokenIcon = () => h('div', { class: 'icon-wrapper token-icon' }, '💰')
const GameIcon = () => h('div', { class: 'icon-wrapper game-icon' }, '🎮')
const RulesIcon = () => h('div', { class: 'icon-wrapper rules-icon' }, '📋')
const RewardIcon = () => h('div', { class: 'icon-wrapper reward-icon' }, '🏆')

const router = useRouter()
const gameStore = useGameStore()

// 当前步骤索引
const currentStepIndex = ref(0)
const demoCompleted = ref(false)

// 教程步骤定义
const tutorialSteps = ref([
  {
    id: 'welcome',
    title: '欢迎来到萌宠世界',
    icon: PetIcon,
    description: [
      '欢迎来到萌宠养成代币游戏！',
      '在这里，你可以养成可爱的萌宠，通过悉心照料提升它们的属性，最终将它们兑换成有价值的代币。',
      '让我们一起开始这段奇妙的旅程吧！'
    ],
    keyPoints: [
      '这是一个基于区块链的萌宠养成游戏',
      '你的萌宠数据会安全存储在本地',
      '养成的萌宠可以兑换成真实的代币'
    ]
  },
  {
    id: 'pet-system',
    title: '萌宠系统介绍',
    icon: PetIcon,
    description: [
      '每只萌宠都有独特的属性和特征：',
      '• 等级：通过喂食和训练提升，影响萌宠的整体能力',
      '• 稀有度：决定萌宠的基础价值，从普通到传说级别',
      '• 健康度：影响萌宠的状态和成长速度',
      '• 装备：可以为萌宠装备道具来增强属性'
    ],
    interactive: true,
    demoComponent: PetIntroDemo,
    keyPoints: [
      '稀有度越高的萌宠价值越大',
      '保持萌宠健康度有助于更好的成长',
      '装备可以显著提升萌宠属性'
    ]
  },
  {
    id: 'token-exchange',
    title: '代币兑换机制',
    icon: TokenIcon,
    description: [
      '当你的萌宠达到一定条件时，可以将其兑换成代币：',
      '• 兑换价值基于萌宠的等级、稀有度、健康度等属性计算',
      '• 兑换会调用智能合约铸造新的代币到你的钱包',
      '• 兑换后萌宠会消失，但你获得了有价值的代币'
    ],
    interactive: true,
    demoComponent: TokenExchangeDemo,
    keyPoints: [
      '兑换是不可逆的操作，请谨慎考虑',
      '代币会直接发送到你的钱包地址',
      '兑换需要支付少量的Gas费用'
    ]
  },
  {
    id: 'game-flow',
    title: '游戏流程指引',
    icon: GameIcon,
    description: [
      '游戏的基本流程如下：',
      '1. 连接你的Web3钱包',
      '2. 创建或领养你的第一只萌宠',
      '3. 通过喂食、训练、装备道具来养成萌宠',
      '4. 当萌宠达到理想状态时，选择兑换成代币',
      '5. 使用获得的代币购买更多道具或直接交易'
    ],
    interactive: true,
    demoComponent: GameFlowDemo,
    keyPoints: [
      '耐心养成是获得高价值萌宠的关键',
      '合理使用道具可以事半功倍',
      '关注萌宠的各项属性平衡发展'
    ]
  },
  {
    id: 'rules-rewards',
    title: '游戏规则与奖励',
    icon: RulesIcon,
    description: [
      '游戏规则：',
      '• 每只萌宠都有生命周期，需要定期照料',
      '• 不同的操作有不同的冷却时间',
      '• 萌宠的成长需要时间，不能急于求成',
      '',
      '奖励机制：',
      '• 连续登录可获得额外奖励',
      '• 完成特定成就可解锁新的萌宠品种',
      '• 参与社区活动可获得稀有道具'
    ],
    keyPoints: [
      '遵守游戏规则，享受公平的游戏环境',
      '积极参与可获得更多奖励',
      '社区互动让游戏更有趣'
    ]
  },
  {
    id: 'complete',
    title: '准备开始游戏',
    icon: RewardIcon,
    description: [
      '恭喜你完成了教程！',
      '现在你已经了解了萌宠养成游戏的基本玩法。',
      '点击"开始游戏"按钮，开始你的萌宠养成之旅吧！',
      '',
      '记住：耐心和爱心是养成优秀萌宠的秘诀。'
    ],
    keyPoints: [
      '随时可以在设置中重新查看教程',
      '遇到问题可以查看帮助文档',
      '祝你在萌宠世界玩得开心！'
    ]
  }
])

// 计算属性
const currentStep = computed(() => tutorialSteps.value[currentStepIndex.value])
const isLastStep = computed(() => currentStepIndex.value === tutorialSteps.value.length - 1)
// 计算进度百分比
const progress = computed(() => {
  return (currentStepIndex.value + 1) / tutorialSteps.value.length * 100
})

// 方法
const handleBack = () => {
  if (currentStepIndex.value > 0) {
    previousStep()
  } else {
    router.back()
  }
}

const nextStep = () => {
  if (currentStepIndex.value < tutorialSteps.value.length - 1) {
    currentStepIndex.value++
    demoCompleted.value = false
    window.scrollTo(0, 0)
  }
}

const previousStep = () => {
  if (currentStepIndex.value > 0) {
    currentStepIndex.value--
    demoCompleted.value = false
    window.scrollTo(0, 0)
  }
}

const goToStep = (index: number) => {
  if (index !== currentStepIndex.value) {
    currentStepIndex.value = index
    demoCompleted.value = false
    window.scrollTo(0, 0)
  }
}

const handleDemoComplete = () => {
  demoCompleted.value = true
  showToast('演示完成！')
}

const completeTutorial = () => {
  // 标记教程已完成
  gameStore.setTutorialCompleted(true)
  showToast('教程完成，欢迎来到萌宠世界！')

  // 检查是否有萌宠，如果没有则跳转到创建页面，否则跳转到萌宠列表
  const petStore = usePetStore()
  if (petStore.pets.length === 0) {
    router.push('/pet/create')
  } else {
    router.push('/pets')
  }
}

const showSkipDialog = () => {
  showDialog({
    title: '跳过教程',
    message: '确定要跳过教程吗？你可以随时在设置中重新查看。',
    confirmButtonText: '跳过',
    cancelButtonText: '继续学习'
  }).then(() => {
    completeTutorial()
  }).catch(() => {
    // 用户选择继续学习
  })
}

// 生命周期
onMounted(() => {
  // 记录教程开始时间
  gameStore.setTutorialStartTime(Date.now())
})
</script>

<style scoped>
/* 通用样式 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

.primary-btn {
  background-color: #0071e3;
  color: white;
  border: none;
  border-radius: 980px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.primary-btn:hover {
  background-color: #0077ed;
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.primary-btn:disabled {
  background-color: #a1a1a6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.secondary-btn {
  background-color: #f5f5f7;
  color: #1d1d1f;
  border: none;
  border-radius: 980px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.secondary-btn:hover {
  background-color: #e5e5e7;
}

.text-btn {
  background-color: transparent;
  color: #0071e3;
  border: none;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.text-btn:hover {
  text-decoration: underline;
}

.icon-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background-color: #f5f5f7;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.icon-btn:hover {
  background-color: #e5e5e7;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 3rem 0 2rem;
  color: white;
  text-align: center;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  color: white;
}

.page-desc {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0 0 1rem 0;
  color: white;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.step-indicator {
  font-size: 0.875rem;
  /* color: #6e6e73; */
  font-weight: 500;
}

/* 进度条 */
.progress-container {
  padding: 0 0 1.5rem;
}

.progress-bar {
  height: 4px;
  background-color: #f5f5f7;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #0071e3;
  transition: width 0.3s ease;
}

/* 教程内容 */
.tutorial-content {
  padding: 2rem 0;
}

.step-content {
  max-width: 800px;
  margin: 0 auto;
}

.step-illustration {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
}

.icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 6rem;
  height: 6rem;
  border-radius: 50%;
  font-size: 3rem;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.pet-icon {
  background: linear-gradient(135deg, #a78bfa, #3b82f6);
}

.token-icon {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
}

.game-icon {
  background: linear-gradient(135deg, #34d399, #10b981);
}

.rules-icon {
  background: linear-gradient(135deg, #60a5fa, #3b82f6);
}

.reward-icon {
  background: linear-gradient(135deg, #f472b6, #ec4899);
}

.step-description {
  margin-bottom: 2rem;
}

.description-paragraph {
  font-size: 1.125rem;
  color: #424245;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.interactive-demo {
  background-color: white;
  border-radius: 18px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
}

.demo-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 1.5rem;
}

.key-points {
  background-color: #f5f5f7;
  border-radius: 18px;
  padding: 2rem;
  margin-bottom: 2rem;
}

.key-points-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 1.5rem;
}

.key-points-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.key-point {
  display: flex;
  margin-bottom: 1rem;
}

.point-icon {
  color: #0071e3;
  font-size: 1.5rem;
  line-height: 1;
  margin-right: 1rem;
}

/* 底部操作按钮 */
.tutorial-actions {
  padding: 2rem 0 4rem;
}

.actions-wrapper {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1.5rem;
}

.complete-btn {
  background: linear-gradient(135deg, #0071e3, #42a5f5);
}

.skip-tutorial {
  text-align: center;
}

/* 侧边导航 */
.side-navigation {
  display: none;
  position: fixed;
  top: 50%;
  right: 2rem;
  transform: translateY(-50%);
  z-index: 10;
}

.steps-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.step-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem;
  border-radius: 980px;
  background-color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
}

.step-item:hover {
  transform: translateX(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.step-item.active {
  background-color: #0071e3;
}

.step-item.active .step-number,
.step-item.active .step-label {
  color: white;
}

.step-item.completed .step-number {
  background-color: #34d399;
  color: white;
}

.step-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.75rem;
  height: 1.75rem;
  border-radius: 50%;
  background-color: #f5f5f7;
  font-size: 0.875rem;
  font-weight: 600;
  color: #1d1d1f;
}

.step-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #1d1d1f;
  white-space: nowrap;
}

/* 动画效果 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 响应式调整 */
@media (min-width: 768px) {
  .page-title {
    font-size: 2.5rem;
  }

  .tutorial-content {
    padding: 3rem 0;
  }

  .icon-wrapper {
    width: 8rem;
    height: 8rem;
    font-size: 4rem;
  }
}

@media (min-width: 1024px) {
  .page-title {
    font-size: 3rem;
  }

  .side-navigation {
    display: block;
  }

  .tutorial-content {
    padding-right: 8rem;
  }
}
</style>
