<template>
  <AppLayoutWithSidebar>
    <!-- 页面头部 -->
    <section class="page-header">
      <div class="container">
        <h1 class="page-title">游戏商店</h1>
        <p class="page-desc">为你的萌宠购买各种道具和装备</p>

        <!-- 用户资产显示 -->
        <div class="user-assets">
          <div class="asset-item">
            <span class="asset-icon">🪙</span>
            <span class="asset-label">金币</span>
            <span class="asset-value">{{ gameStore.coins }}</span>
          </div>
          <div class="asset-item">
            <span class="asset-icon">💎</span>
            <span class="asset-label">代币</span>
            <span class="asset-value">{{ formatTokens(gameStore.tokens) }}</span>
          </div>
          <div class="asset-item">
            <span class="asset-icon">🎒</span>
            <span class="asset-label">背包</span>
            <span class="asset-value">{{ inventoryCount }}/100</span>
          </div>
        </div>
      </div>
    </section>

    <!-- 商店内容区域 -->
    <section class="shop-content-section">
      <div class="container">
        <!-- 搜索和筛选 -->
        <div class="shop-filters">
          <van-search
            v-model="searchQuery"
            placeholder="搜索商品..."
            @search="handleSearch"
            @clear="handleClearSearch"
            shape="round"
          />

          <div class="filter-row">
            <van-dropdown-menu class="full-width-dropdown">
              <van-dropdown-item v-model="selectedCategory" :options="categoryOptions" />
              <van-dropdown-item v-model="sortBy" :options="sortOptions" />
              <van-dropdown-item v-model="priceFilter" :options="priceFilterOptions" />
            </van-dropdown-menu>
          </div>
        </div>

        <!-- 推荐商品 -->
        <div v-if="recommendedItems.length > 0" class="recommended-section">
          <h3 class="section-title">
            <van-icon name="star" />
            为您推荐
          </h3>
          <van-swipe :autoplay="3000" indicator-color="white" class="mobile-swipe">
            <van-swipe-item v-for="item in recommendedItems" :key="item.id">
              <ShopItemCardSimple :item="item" :is-recommended="true" />
            </van-swipe-item>
          </van-swipe>
        </div>

        <!-- 限时特惠 -->
        <div v-if="limitedTimeItems.length > 0" class="limited-section">
          <h3 class="section-title">
            <van-icon name="clock" />
            限时特惠
            <van-count-down
              :time="timeUntilRefresh"
              format="HH:mm:ss"
              class="countdown"
            />
          </h3>
          <div class="limited-items">
            <ShopItemCardSimple
              v-for="item in limitedTimeItems"
              :key="item.id"
              :item="item"
              class="limited-item"
            />
          </div>
        </div>

        <!-- 商品分类标签 -->
        <van-tabs v-model:active="activeTab" @change="handleTabChange" sticky swipeable animated class="shop-tabs">
          <van-tab title="全部" name="all" />
          <van-tab title="食物" name="food" />
          <van-tab title="玩具" name="toy" />
          <van-tab title="装备" name="equipment" />
          <van-tab title="药品" name="medicine" />
          <van-tab title="训练" name="training" />
        </van-tabs>

        <!-- 商品列表 -->
        <div class="shop-content">
          <van-loading v-if="shopStore.isLoading" class="loading-center">
            加载中...
          </van-loading>

          <van-empty v-else-if="filteredItems.length === 0" description="暂无商品" />

          <div v-else class="items-grid">
            <ShopItemCardSimple
              v-for="item in paginatedItems"
              :key="item.id"
              :item="item"
            />
          </div>

          <!-- 萌系分页器 -->
          <div v-if="totalPages > 1" class="cute-pagination">
            <div class="pagination-container">
              <!-- 上一页按钮 -->
              <button
                class="page-btn prev-btn"
                :disabled="currentPage === 1"
                @click="currentPage--"
              >
                <span class="btn-icon">🐾</span>
                <span class="btn-text">上一页</span>
                <div class="btn-sparkle"></div>
              </button>

              <!-- 页码指示器 -->
              <div class="page-indicator">
                <div class="page-dots">
                  <span
                    v-for="page in visiblePages"
                    :key="page"
                    class="page-dot"
                    :class="{ active: page === currentPage }"
                    @click="currentPage = page"
                  >
                    {{ page }}
                  </span>
                </div>
                <div class="page-info">
                  <span class="current-page">{{ currentPage }}</span>
                  <span class="separator">/</span>
                  <span class="total-pages">{{ totalPages }}</span>
                </div>
              </div>

              <!-- 下一页按钮 -->
              <button
                class="page-btn next-btn"
                :disabled="currentPage === totalPages"
                @click="currentPage++"
              >
                <span class="btn-text">下一页</span>
                <span class="btn-icon">🐾</span>
                <div class="btn-sparkle"></div>
              </button>
            </div>
          </div>
        </div>

        <!-- 快速操作按钮 -->
        <div class="quick-actions">
          <van-button
            type="primary"
            size="small"
            @click="refreshShop"
            :loading="shopStore.isLoading"
            class="refresh-btn"
          >
            <van-icon name="replay" />
            刷新商店
          </van-button>
        </div>

        <!-- 背包管理弹窗 -->
        <van-popup
          v-model:show="showInventory"
          position="right"
          :style="{ width: '100%', height: '100%' }"
        >
          <div class="inventory-placeholder">
            <van-empty description="背包功能开发中..." />
            <van-button @click="showInventory = false" type="primary">关闭</van-button>
          </div>
        </van-popup>

        <!-- 购买确认弹窗 -->
        <van-dialog
          v-model:show="showPurchaseDialog"
          title="购买确认"
          :message="purchaseDialogMessage"
          show-cancel-button
          @confirm="confirmPurchase"
          @cancel="cancelPurchase"
        />

        <!-- 购买成功提示 -->
        <van-notify
          v-model:show="showSuccessNotify"
          type="success"
          :message="successMessage"
        />
      </div>
    </section>
  </AppLayoutWithSidebar>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { showToast } from 'vant'
import { useShopStore } from '../stores/shop'
import { useGameStore } from '../stores/game'
import AppLayoutWithSidebar from '../components/layout/AppLayoutWithSidebar.vue'
import ShopItemCardSimple from '../components/shop/ShopItemCardSimple.vue'
// import InventoryManager from '../components/shop/InventoryManager.vue'

const shopStore = useShopStore()
const gameStore = useGameStore()

// 响应式数据
const searchQuery = ref('')
const selectedCategory = ref('all')
const sortBy = ref('default')
const priceFilter = ref('all')
const activeTab = ref('all')
const currentPage = ref(1)
const itemsPerPage = 12
const showInventory = ref(false)
const showPurchaseDialog = ref(false)
const purchaseDialogMessage = ref('')
const showSuccessNotify = ref(false)
const successMessage = ref('')

// 选项配置
const categoryOptions = [
  { text: '全部分类', value: 'all' },
  { text: '食物', value: 'food' },
  { text: '玩具', value: 'toy' },
  { text: '装备', value: 'equipment' },
  { text: '药品', value: 'medicine' },
  { text: '训练用品', value: 'training' }
]

const sortOptions = [
  { text: '默认排序', value: 'default' },
  { text: '价格从低到高', value: 'price_asc' },
  { text: '价格从高到低', value: 'price_desc' },
  { text: '稀有度', value: 'rarity' },
  { text: '名称', value: 'name' }
]

const priceFilterOptions = [
  { text: '全部价格', value: 'all' },
  { text: '0-50金币', value: '0-50' },
  { text: '51-100金币', value: '51-100' },
  { text: '101-200金币', value: '101-200' },
  { text: '200+金币', value: '200+' }
]

// 计算属性
const inventoryCount = computed(() =>
  gameStore.inventory.reduce((sum, item) => sum + item.quantity, 0)
)

const recommendedItems = computed(() =>
  shopStore.getRecommendedItems(3)
)

const limitedTimeItems = computed(() =>
  shopStore.limitedTimeItems.slice(0, 3)
)

const timeUntilRefresh = computed(() => {
  // 计算到下次刷新的时间（简化实现）
  const now = Date.now()
  const nextRefresh = shopStore.lastRefreshTime + 24 * 60 * 60 * 1000 // 24小时后
  return Math.max(0, nextRefresh - now)
})

const filteredItems = computed(() => {
  let items = [...shopStore.shopItems]

  // 搜索筛选
  if (searchQuery.value) {
    items = shopStore.searchItems(searchQuery.value)
  }

  // 分类筛选
  if (activeTab.value !== 'all') {
    items = items.filter(item => item.category === activeTab.value)
  }

  if (selectedCategory.value !== 'all' && selectedCategory.value !== activeTab.value) {
    items = items.filter(item => item.category === selectedCategory.value)
  }

  // 价格筛选
  if (priceFilter.value !== 'all') {
    const [min, max] = priceFilter.value.split('-').map(Number)
    if (max) {
      items = items.filter(item => item.price >= min && item.price <= max)
    } else {
      items = items.filter(item => item.price >= min)
    }
  }

  // 排序
  switch (sortBy.value) {
    case 'price_asc':
      items = shopStore.sortItemsByPrice(true)
      break
    case 'price_desc':
      items = shopStore.sortItemsByPrice(false)
      break
    case 'rarity':
      items = shopStore.sortItemsByRarity()
      break
    case 'name':
      items.sort((a, b) => a.name.localeCompare(b.name))
      break
    default:
      // 保持默认顺序
      break
  }

  return items
})

const totalPages = computed(() =>
  Math.ceil(filteredItems.value.length / itemsPerPage)
)

const visiblePages = computed(() => {
  const total = totalPages.value
  const current = currentPage.value
  const pages = []

  if (total <= 5) {
    // 如果总页数小于等于5，显示所有页码
    for (let i = 1; i <= total; i++) {
      pages.push(i)
    }
  } else {
    // 否则显示当前页前后各2页
    const start = Math.max(1, current - 2)
    const end = Math.min(total, current + 2)

    for (let i = start; i <= end; i++) {
      pages.push(i)
    }
  }

  return pages
})

const paginatedItems = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage
  const end = start + itemsPerPage
  return filteredItems.value.slice(start, end)
})

// 方法
const formatTokens = (tokens: string): string => {
  const num = parseFloat(tokens)
  if (num === 0) return '0'
  if (num < 0.001) return '<0.001'
  return num.toFixed(3)
}

const handleSearch = () => {
  currentPage.value = 1
}

const handleClearSearch = () => {
  searchQuery.value = ''
  currentPage.value = 1
}

const handleTabChange = () => {
  currentPage.value = 1
  selectedCategory.value = activeTab.value
}

const refreshShop = async () => {
  try {
    shopStore.refreshShop()
    showToast({
      type: 'success',
      message: '商店已刷新'
    })
  } catch (error) {
    console.error('刷新商店失败:', error)
    showToast({
      type: 'fail',
      message: '刷新失败，请重试'
    })
  }
}

const handleUseItem = (item: any) => {
  showToast({
    type: 'success',
    message: `使用了 ${item.name}`
  })
}

const confirmPurchase = () => {
  showPurchaseDialog.value = false
  // 购买逻辑已在 ShopItemCard 中处理
}

const cancelPurchase = () => {
  showPurchaseDialog.value = false
}

// 监听筛选条件变化重置分页
watch([selectedCategory, sortBy, priceFilter, searchQuery], () => {
  currentPage.value = 1
})

// 组件挂载时初始化商店
onMounted(() => {
  shopStore.initializeShop()
})
</script>

<style scoped>
/* 页面头部样式 */
.page-header {
  /* background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 50%, #fd79a8 100%); */
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 3rem 0 2rem;
  color: white;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.3)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.2)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.4)"/><circle cx="70" cy="70" r="1.2" fill="rgba(255,255,255,0.3)"/></svg>') repeat;
  animation: float 20s ease-in-out infinite;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
}

.page-desc {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0 0 2rem 0;
  position: relative;
  z-index: 1;
}

/* 用户资产样式 */
.user-assets {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  max-width: 600px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.asset-item {
  background: rgba(255, 255, 255, 0.25);
  border-radius: 20px;
  padding: 1.5rem 1rem;
  backdrop-filter: blur(15px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.asset-item:hover {
  transform: translateY(-5px) scale(1.05);
  background: rgba(255, 255, 255, 0.35);
  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.3);
}

.asset-icon {
  font-size: 2rem;
  margin-bottom: 0.75rem;
  animation: bounce 2s ease-in-out infinite;
}

.asset-label {
  font-size: 0.9rem;
  opacity: 0.9;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.asset-value {
  font-size: 1.3rem;
  font-weight: 700;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 商店内容区域样式 */
.shop-content-section {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #fef3c7 100%);
  min-height: calc(100vh - 200px);
  padding: 2rem 0;
  position: relative;
  width: 100%;
  overflow-x: hidden;
}

.shop-content-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200"><circle cx="50" cy="50" r="3" fill="rgba(255,182,193,0.3)"/><circle cx="150" cy="100" r="2" fill="rgba(255,218,185,0.4)"/><circle cx="100" cy="150" r="2.5" fill="rgba(255,240,245,0.3)"/></svg>') repeat;
  opacity: 0.6;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  position: relative;
  z-index: 1;
  width: 100%;
  box-sizing: border-box;
}

.shop-filters {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 25px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  padding: 2rem;
  backdrop-filter: blur(20px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.shop-filters:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.filter-row {
  padding: 1rem 0;
}

.recommended-section,
.limited-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 25px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  padding: 2.5rem;
  backdrop-filter: blur(20px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.recommended-section::before,
.limited-section::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 182, 193, 0.1) 0%, transparent 70%);
  animation: rotate 20s linear infinite;
  pointer-events: none;
}

.recommended-section:hover,
.limited-section:hover {
  transform: translateY(-4px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  color: #1f2937;
  position: relative;
  z-index: 1;
}

.section-title .van-icon {
  margin-right: 0.75rem;
  color: #f59e0b;
  font-size: 1.5rem;
  animation: pulse 2s ease-in-out infinite;
}

.countdown {
  margin-left: auto;
  color: #ef4444;
  font-family: 'Courier New', monospace;
  font-weight: 700;
  background: rgba(239, 68, 68, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 15px;
  border: 2px solid rgba(239, 68, 68, 0.2);
}

.limited-items {
  display: flex;
  gap: 1.5rem;
  overflow-x: auto;
  padding-bottom: 1rem;
  scroll-behavior: smooth;
}

.limited-items::-webkit-scrollbar {
  height: 8px;
}

.limited-items::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 10px;
}

.limited-items::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #ffeaa7, #fd79a8);
  border-radius: 10px;
}

.limited-item {
  flex-shrink: 0;
  width: 18rem;
  transition: all 0.3s ease;
}

.limited-item:hover {
  transform: scale(1.05);
}

.shop-content {
  padding: 1rem;
}

.loading-center {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 8rem;
}

.items-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 640px) {
  .items-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .items-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) {
  .items-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* 萌系分页器样式 */
.cute-pagination {
  margin: 3rem 0 2rem;
  display: flex;
  justify-content: center;
}

.pagination-container {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  background: rgba(255, 255, 255, 0.95);
  padding: 1.5rem 2rem;
  border-radius: 25px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.page-btn {
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
  border: none;
  border-radius: 20px;
  color: white;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(255, 234, 167, 0.4);
}

.page-btn:hover:not(:disabled) {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 8px 25px rgba(255, 234, 167, 0.6);
  background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-btn .btn-icon {
  font-size: 1.1rem;
  animation: bounce 2s ease-in-out infinite;
}

.page-btn .btn-text {
  font-weight: 600;
}

.page-btn .btn-sparkle {
  position: absolute;
  top: 5px;
  right: 5px;
  width: 6px;
  height: 6px;
  background: radial-gradient(circle, #fff 0%, transparent 70%);
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.page-btn:hover:not(:disabled) .btn-sparkle {
  opacity: 1;
  animation: sparkle 1.5s ease-in-out infinite;
}

.page-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
}

.page-dots {
  display: flex;
  gap: 0.5rem;
}

.page-dot {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  color: #6b7280;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.page-dot:hover {
  background: rgba(255, 234, 167, 0.8);
  color: #1f2937;
  transform: scale(1.1);
}

.page-dot.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transform: scale(1.2);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
  border-color: rgba(255, 255, 255, 0.3);
}

.page-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #6b7280;
  font-weight: 600;
}

.current-page {
  color: #667eea;
  font-weight: 700;
  font-size: 1.1rem;
}

.separator {
  color: #d1d5db;
  font-weight: 400;
}

.total-pages {
  color: #9ca3af;
}

.quick-actions {
  position: fixed;
  bottom: 1rem;
  right: 1rem;
  z-index: 10;
}

.refresh-btn {
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50px;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.refresh-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.refresh-btn .van-icon {
  margin-right: 0.25rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-title {
    font-size: 2rem;
  }

  .user-assets {
    padding: 1rem;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }

  .asset-item {
    padding: 0.75rem;
    font-size: 0.9rem;
  }

  .shop-filters {
    margin: 0 0.5rem 1rem;
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
    width: auto;
  }

  .filter-group {
    width: 100%;
  }

  .full-width-dropdown {
    width: 100%;
  }

  .full-width-dropdown :deep(.van-dropdown-menu__bar) {
    width: 100%;
  }

  .full-width-dropdown :deep(.van-dropdown-menu__item) {
    flex: 1;
  }

  .shop-tabs :deep(.van-tab) {
    flex: none;
    min-width: auto;
    padding: 0 12px;
  }

  .recommended-section,
  .limited-section {
    margin: 0 0.5rem 1rem;
    padding: 1rem;
    width: auto;
  }

  .items-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .limited-items {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .container {
    padding: 0 0.5rem;
    width: 100%;
    max-width: 100%;
  }

  .shop-content-section {
    padding: 1rem 0;
    width: 100%;
  }

  .inventory-placeholder {
    padding: 2rem 1rem;
    text-align: center;
  }

  /* 分页器移动端优化 */
  .pagination-container {
    flex-direction: column;
    gap: 1rem;
    padding: 1.25rem 1.5rem;
  }

  .page-btn {
    padding: 0.6rem 1.25rem;
    font-size: 0.85rem;
  }

  .page-btn .btn-icon {
    font-size: 1rem;
  }

  .page-indicator {
    order: -1;
  }

  .page-dots {
    gap: 0.4rem;
  }

  .page-dot {
    width: 2.2rem;
    height: 2.2rem;
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .page-header {
    padding: 2rem 0 1.5rem;
  }

  .page-title {
    font-size: 1.75rem;
  }

  .user-assets {
    grid-template-columns: 1fr;
    gap: 0.5rem;
    padding: 0.75rem;
  }

  .asset-item {
    padding: 0.5rem;
    font-size: 0.8rem;
  }

  .shop-filters {
    padding: 0.75rem;
    margin: 0 0.25rem 1rem;
    width: auto;
  }

  .recommended-section,
  .limited-section {
    padding: 0.75rem;
    margin: 0 0.25rem 1rem;
    width: auto;
  }

  .items-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .container {
    padding: 0 0.25rem;
    width: 100%;
    max-width: 100%;
  }

  .section-title {
    font-size: 1.25rem;
  }

  /* 小屏幕分页器优化 */
  .cute-pagination {
    margin: 2rem 0 1.5rem;
  }

  .pagination-container {
    padding: 1rem;
    gap: 0.75rem;
  }

  .page-btn {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
    border-radius: 15px;
  }

  .page-btn .btn-text {
    display: none;
  }

  .page-btn .btn-icon {
    font-size: 1.2rem;
  }

  .page-dots {
    gap: 0.3rem;
    flex-wrap: wrap;
    justify-content: center;
  }

  .page-dot {
    width: 2rem;
    height: 2rem;
    font-size: 0.75rem;
  }

  .page-info {
    font-size: 0.8rem;
  }
}

/* 动画效果 */
.shop-view {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.recommended-section,
.limited-section {
  animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 萌系动画关键帧 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0) scale(1);
  }
  40% {
    transform: translateY(-8px) scale(1.1);
  }
  60% {
    transform: translateY(-4px) scale(1.05);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes sparkle {
  0%, 100% {
    opacity: 0;
    transform: scale(0) rotate(0deg);
  }
  50% {
    opacity: 1;
    transform: scale(1) rotate(180deg);
  }
}

@keyframes wiggle {
  0%, 100% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(-3deg);
  }
  75% {
    transform: rotate(3deg);
  }
}

@keyframes heartbeat {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* 添加 Vant 组件移动端适配 */
.full-width-dropdown {
  width: 100%;
}

.mobile-swipe {
  width: 100%;
  overflow: hidden;
}

.shop-tabs {
  width: 100%;
}

.shop-tabs :deep(.van-tabs__nav) {
  width: 100%;
}

.shop-tabs :deep(.van-tabs__content) {
  width: 100%;
}
</style>
