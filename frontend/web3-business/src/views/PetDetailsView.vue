<template>
  <div class="pet-details-page">
    <!-- 顶部导航栏 -->
    <header class="page-nav">
      <button class="nav-btn back-btn" @click="handleBack" aria-label="返回">
        <van-icon name="arrow-left" size="20" />
      </button>
      <h1 class="nav-title">🐾 萌宠详情</h1>
      <button class="nav-btn settings-btn" @click="showSettings = true" aria-label="设置">
        <van-icon name="setting-o" size="20" />
      </button>
    </header>

    <!-- 主要内容区域 -->
    <main class="pet-details-content" v-if="currentPet">
      <div class="container">
        <!-- 萌宠基本信息和展示 -->
        <section class="pet-showcase">
          <div class="pet-display">
            <PetDisplay
              :pet="currentPet"
              size="huge"
              :interactive="true"
              :effects="['rarity', 'animation']"
              animation="idle"
            />
          </div>

          <div class="pet-info">
            <div class="pet-name-section">
              <h2 class="pet-name">{{ currentPet.name }}</h2>
              <button class="edit-name-btn" @click="handleEditName">
                <van-icon name="edit" />
              </button>
            </div>

            <div class="pet-meta">
              <span class="pet-type">{{ getPetTypeName(currentPet.type) }}</span>
              <span class="pet-rarity" :class="getRarityClass(currentPet.rarity)">
                {{ getRarityName(currentPet.rarity) }}
              </span>
              <span class="pet-level">等级 {{ currentPet.level }}</span>
            </div>

            <!-- 宠物说明 -->
            <div class="pet-description">
              <p>{{ currentPet.description || '这是一只可爱的萌宠，等待着你的关爱和陪伴！拥有独特的个性和技能，是你在区块链世界中最忠实的伙伴。' }}</p>
            </div>

            <div class="pet-actions">
              <button class="primary-btn" @click="showEvolution = true">
                查看进化路线
              </button>
              <button class="secondary-btn" @click="handleCalculateValue">
                查看价值
              </button>
            </div>
          </div>
        </section>

        <!-- 萌宠属性面板 -->
        <section class="stats-section">
          <h3 class="section-title">属性面板</h3>
          <PetStatsPanel
            :pet="currentPet"
            :show-detailed="true"
            @stat-click="handleStatClick"
          />
        </section>

        <!-- 萌宠状态监控 -->
        <section class="status-section">
          <h3 class="section-title">状态监控</h3>
          <PetStatusMonitor
            :pet="currentPet"
            @feed="handleFeed"
            @play="handlePlay"
            @rest="handleRest"
            @train="handleTrain"
          />
        </section>

        <!-- 萌宠装备管理 -->
        <section class="equipment-section">
          <h3 class="section-title">装备管理</h3>
          <PetEquipmentManager
            :pet="currentPet"
            :inventory="inventory"
            @equip-item="handleEquipItem"
            @unequip-item="handleUnequipItem"
            @view-equipment="handleViewEquipment"
          />
        </section>

        <!-- 萌宠技能和特质 -->
        <section class="skills-section">
          <h3 class="section-title">技能与特质</h3>
          <PetSkillsTraits
            :pet="currentPet"
            @learn-skill="handleLearnSkill"
            @upgrade-trait="handleUpgradeTrait"
          />
        </section>

        <!-- 萌宠价值评估 -->
        <section class="value-section">
          <h3 class="section-title">价值评估</h3>
          <PetValueAssessment
            :pet="currentPet"
            @calculate-value="handleCalculateValue"
            @exchange-tokens="handleExchangeTokens"
          />
        </section>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <div class="container">
        <div class="empty-icon">🔍</div>
        <h3 class="empty-title">未找到萌宠信息</h3>
        <p class="empty-desc">该萌宠可能不存在或已被删除</p>
        <button class="primary-btn" @click="handleBack">返回首页</button>
      </div>
    </div>

    <!-- 弹窗组件 -->
    <!-- 编辑名称弹窗 -->
    <van-dialog
      v-model:show="showNameEdit"
      title="编辑萌宠名称"
      show-cancel-button
      @confirm="confirmNameEdit"
      class="custom-dialog"
    >
      <div class="p-4">
        <van-field
          v-model="newPetName"
          placeholder="请输入新名称"
          maxlength="20"
          show-word-limit
        />
      </div>
    </van-dialog>

    <!-- 进化路径弹窗 -->
    <van-popup
      v-model:show="showEvolution"
      position="bottom"
      :style="{ height: '70%' }"
      class="custom-popup"
    >
      <PetEvolutionPath
        v-if="currentPet"
        :pet="currentPet"
        @close="showEvolution = false"
        @evolve="handleEvolve"
      />
    </van-popup>

    <!-- 装备详情弹窗 -->
    <van-popup
      v-model:show="showEquipmentDetail"
      position="center"
      :style="{ width: '90%', maxWidth: '400px' }"
      class="custom-popup"
    >
      <EquipmentDetailCard
        v-if="selectedEquipment"
        :equipment="selectedEquipment"
        @close="showEquipmentDetail = false"
        @enhance="handleEnhanceEquipment"
        @repair="handleRepairEquipment"
      />
    </van-popup>

    <!-- 设置弹窗 -->
    <van-popup
      v-model:show="showSettings"
      position="bottom"
      :style="{ height: '50%' }"
      class="custom-popup"
    >
      <PetSettingsPanel
        :pet="currentPet"
        @close="showSettings = false"
        @export="handleExportPet"
        @reset="handleResetPet"
      />
    </van-popup>

    <!-- 加载状态 -->
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <p class="loading-text">加载中...</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showToast, showDialog } from 'vant'
import { usePetStore } from '../stores/pet'
import { useGameStore } from '../stores/game'
import type { Pet, Equipment, Item, PetRarity, PetType } from '../types/typesWithoutCircular'
import AppLayout from '../components/layout/AppLayout.vue'
import PetDisplay from '../components/pet/PetDisplay.vue'

// 导入组件
import PetBasicInfoCard from '../components/pet/PetBasicInfoCard.vue'
import PetStatsPanel from '../components/pet/PetStatsPanel.vue'
import PetEquipmentManager from '../components/pet/PetEquipmentManager.vue'
import PetStatusMonitor from '../components/pet/PetStatusMonitor.vue'
import PetSkillsTraits from '../components/pet/PetSkillsTraits.vue'
import PetValueAssessment from '../components/pet/PetValueAssessment.vue'
import PetEvolutionPath from '../components/pet/PetEvolutionPath.vue'
import EquipmentDetailCard from '../components/equipment/EquipmentDetailCard.vue'
import PetSettingsPanel from '../components/pet/PetSettingsPanel.vue'

const route = useRoute()
const router = useRouter()
const petStore = usePetStore()
const gameStore = useGameStore()

// 响应式状态
const loading = ref(false)
const showNameEdit = ref(false)
const showEvolution = ref(false)
const showEquipmentDetail = ref(false)
const showSettings = ref(false)
const newPetName = ref('')
const selectedEquipment = ref<Equipment | null>(null)

// 计算属性
const currentPet = computed(() => {
  const petId = route.params.id as string
  return petStore.getPetById(petId)
})

const inventory = computed(() => gameStore.inventory)

// 方法
const handleBack = () => {
  router.back()
}

const handleEditName = () => {
  if (currentPet.value) {
    newPetName.value = currentPet.value.name
    showNameEdit.value = true
  }
}

const confirmNameEdit = async () => {
  if (!currentPet.value || !newPetName.value.trim()) {
    showToast('请输入有效的名称')
    return
  }

  try {
    loading.value = true
    await petStore.updatePetName(currentPet.value.id, newPetName.value.trim())
    showToast('名称修改成功')
    showNameEdit.value = false
  } catch (error) {
    showToast('名称修改失败')
    console.error('Failed to update pet name:', error)
  } finally {
    loading.value = false
  }
}

const handleStatClick = (statName: string, value: number) => {
  showToast(`${statName}: ${value}`)
}

const handleEquipItem = async (equipment: Equipment) => {
  if (!currentPet.value) return

  try {
    loading.value = true
    await petStore.equipItem(currentPet.value.id, equipment)
    showToast(`已装备 ${equipment.name}`)
  } catch (error) {
    showToast('装备失败')
    console.error('Failed to equip item:', error)
  } finally {
    loading.value = false
  }
}

const handleUnequipItem = async (equipment: Equipment) => {
  if (!currentPet.value) return

  try {
    loading.value = true
    await petStore.unequipItem(currentPet.value.id, equipment.id)
    showToast(`已卸下 ${equipment.name}`)
  } catch (error) {
    showToast('卸下装备失败')
    console.error('Failed to unequip item:', error)
  } finally {
    loading.value = false
  }
}

const handleViewEquipment = (equipment: Equipment) => {
  selectedEquipment.value = equipment
  showEquipmentDetail.value = true
}

const handleFeed = async () => {
  if (!currentPet.value) return

  try {
    loading.value = true
    await petStore.feedPet(currentPet.value.id)
    showToast('喂食成功')
  } catch (error) {
    showToast('喂食失败')
    console.error('Failed to feed pet:', error)
  } finally {
    loading.value = false
  }
}

const handlePlay = async () => {
  if (!currentPet.value) return

  try {
    loading.value = true
    await petStore.playWithPet(currentPet.value.id)
    showToast('玩耍成功')
  } catch (error) {
    showToast('玩耍失败')
    console.error('Failed to play with pet:', error)
  } finally {
    loading.value = false
  }
}

const handleRest = async () => {
  if (!currentPet.value) return

  try {
    loading.value = true
    await petStore.restPet(currentPet.value.id)
    showToast('休息成功')
  } catch (error) {
    showToast('休息失败')
    console.error('Failed to rest pet:', error)
  } finally {
    loading.value = false
  }
}

const handleTrain = async () => {
  if (!currentPet.value) return

  try {
    loading.value = true
    await petStore.trainPet(currentPet.value.id, 'general')
    showToast('训练成功')
  } catch (error) {
    showToast('训练失败')
    console.error('Failed to train pet:', error)
  } finally {
    loading.value = false
  }
}

const handleLearnSkill = async (skillId: string) => {
  if (!currentPet.value) return

  try {
    loading.value = true
    await petStore.learnSkill(currentPet.value.id, skillId)
    showToast('技能学习成功')
  } catch (error) {
    showToast('技能学习失败')
    console.error('Failed to learn skill:', error)
  } finally {
    loading.value = false
  }
}

const handleUpgradeTrait = async (traitId: string) => {
  if (!currentPet.value) return

  try {
    loading.value = true
    await petStore.upgradeTrait(currentPet.value.id, traitId)
    showToast('特质升级成功')
  } catch (error) {
    showToast('特质升级失败')
    console.error('Failed to upgrade trait:', error)
  } finally {
    loading.value = false
  }
}

const handleCalculateValue = async () => {
  if (!currentPet.value) return

  try {
    loading.value = true
    const value = await petStore.calculatePetValue(currentPet.value.id)
    showToast(`萌宠价值: ${value} 代币`)
  } catch (error) {
    showToast('价值计算失败')
    console.error('Failed to calculate pet value:', error)
  } finally {
    loading.value = false
  }
}

const handleExchangeTokens = async () => {
  if (!currentPet.value) return

  const confirmed = await showDialog({
    title: '确认兑换',
    message: '兑换后萌宠将被销毁，是否继续？',
    confirmButtonText: '确认兑换',
    cancelButtonText: '取消'
  }).catch(() => false)

  if (confirmed) {
    try {
      loading.value = true
      await petStore.exchangePetForTokens(currentPet.value.id)
      showToast('兑换成功')
      router.push('/')
    } catch (error) {
      showToast('兑换失败')
      console.error('Failed to exchange pet for tokens:', error)
    } finally {
      loading.value = false
    }
  }
}

const handleEvolve = async (evolutionPath: string) => {
  if (!currentPet.value) return

  try {
    loading.value = true
    await petStore.evolvePet(currentPet.value.id, evolutionPath)
    showToast('进化成功')
    showEvolution.value = false
  } catch (error) {
    showToast('进化失败')
    console.error('Failed to evolve pet:', error)
  } finally {
    loading.value = false
  }
}

const handleEnhanceEquipment = async (equipmentId: string, level: number) => {
  try {
    loading.value = true
    await gameStore.enhanceEquipment(equipmentId, level)
    showToast('强化成功')
  } catch (error) {
    showToast('强化失败')
    console.error('Failed to enhance equipment:', error)
  } finally {
    loading.value = false
  }
}

const handleRepairEquipment = async (equipmentId: string) => {
  try {
    loading.value = true
    await gameStore.repairEquipment(equipmentId)
    showToast('修理成功')
  } catch (error) {
    showToast('修理失败')
    console.error('Failed to repair equipment:', error)
  } finally {
    loading.value = false
  }
}

const handleExportPet = async () => {
  if (!currentPet.value) return

  try {
    const exportData = await petStore.exportPet(currentPet.value.id)
    // 创建下载链接
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${currentPet.value.name}_export.json`
    a.click()
    URL.revokeObjectURL(url)
    showToast('导出成功')
  } catch (error) {
    showToast('导出失败')
    console.error('Failed to export pet:', error)
  }
}

const handleResetPet = async () => {
  if (!currentPet.value) return

  const confirmed = await showDialog({
    title: '确认重置',
    message: '重置后萌宠将恢复到初始状态，是否继续？',
    confirmButtonText: '确认重置',
    cancelButtonText: '取消'
  }).catch(() => false)

  if (confirmed) {
    try {
      loading.value = true
      await petStore.resetPet(currentPet.value.id)
      showToast('重置成功')
      showSettings.value = false
    } catch (error) {
      showToast('重置失败')
      console.error('Failed to reset pet:', error)
    } finally {
      loading.value = false
    }
  }
}

// 辅助函数
const getRarityName = (rarity: PetRarity): string => {
  const names = {
    common: '普通',
    uncommon: '不常见',
    rare: '稀有',
    epic: '史诗',
    legendary: '传说',
    mythical: '神话'
  }
  return names[rarity] || rarity
}

const getRarityClass = (rarity: PetRarity): string => {
  return `rarity-${rarity}`
}

const getPetTypeName = (type: PetType): string => {
  const names = {
    cat: '猫咪',
    dog: '狗狗',
    bird: '鸟类',
    other: '其他'
  }
  return names[type] || type
}

// 生命周期
onMounted(() => {
  if (!currentPet.value) {
    showToast('萌宠不存在')
    router.push('/')
  }
})

// 监听路由变化
watch(() => route.params.id, (newId) => {
  if (newId && !currentPet.value) {
    showToast('萌宠不存在')
    router.push('/')
  }
})
</script>

<style scoped>
/* 页面容器 */
.pet-details-page {
  min-height: 100vh;
  background: linear-gradient(to bottom, #f8fafc, #e2e8f0);
}

/* 顶部导航栏 */
.page-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.nav-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.nav-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.nav-title {
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0;
  text-align: center;
  flex: 1;
}

/* 通用样式 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

.section-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1d1d1f;
  margin-bottom: 1.5rem;
  position: relative;
  z-index: 1;
  text-align: center;
}

.section-title::before {
  content: '✨';
  margin-right: 0.5rem;
  font-size: 1.2rem;
}

.section-title::after {
  content: '✨';
  margin-left: 0.5rem;
  font-size: 1.2rem;
}

.primary-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 25px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
}

.primary-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.primary-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.primary-btn:hover::before {
  left: 100%;
}

.secondary-btn {
  background: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 25px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.secondary-btn:hover {
  background: rgba(255, 255, 255, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}



/* 主要内容区域 */
.pet-details-content {
  padding: 2rem 0;
}

/* 萌宠展示区域 */
.pet-showcase {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 3rem;
  background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 50%, #fd79a8 100%);
  border-radius: 25px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.pet-showcase::before {
  content: '✨';
  position: absolute;
  top: 1rem;
  right: 1rem;
  font-size: 1.5rem;
  animation: twinkle 2s infinite;
}

.pet-showcase::after {
  content: '🌟';
  position: absolute;
  bottom: 1rem;
  left: 1rem;
  font-size: 1.2rem;
  animation: twinkle 2s infinite 1s;
}

@keyframes twinkle {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.5; transform: scale(1.2); }
}

.pet-display {
  width: 100%;
  max-width: 300px;
  height: 300px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 2rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.pet-info {
  width: 100%;
  text-align: center;
  color: white;
}

.pet-name-section {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.pet-meta {
  display: flex;
  gap: 0.75rem;
  justify-content: center;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

/* 宠物说明 */
.pet-description {
  margin: 1.5rem 0;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  backdrop-filter: blur(5px);
}

.pet-description p {
  margin: 0;
  font-size: 0.95rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.95);
}

.pet-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.pet-name-section {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.5rem;
}

.pet-name {
  font-size: 2rem;
  font-weight: 700;
  color: white;
  margin: 0;
  margin-right: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.edit-name-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.75rem;
  height: 1.75rem;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  color: white;
}

.edit-name-btn:hover {
  background: rgba(255, 255, 255, 0.4);
  transform: scale(1.1);
}

.pet-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.pet-type,
.pet-rarity,
.pet-level {
  font-size: 0.875rem;
  padding: 0.4rem 1rem;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  color: white;
  font-weight: 600;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.rarity-common {
  color: #6b7280;
}

.rarity-uncommon {
  color: #10b981;
}

.rarity-rare {
  color: #3b82f6;
}

.rarity-epic {
  color: #8b5cf6;
}

.rarity-legendary {
  color: #f59e0b;
}

.rarity-mythical {
  color: #ef4444;
}

.pet-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: center;
}

/* 内容区域 */
.stats-section,
.status-section,
.equipment-section,
.skills-section,
.value-section {
  margin-bottom: 3rem;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  backdrop-filter: blur(10px);
  border-radius: 25px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.stats-section::before,
.status-section::before,
.equipment-section::before,
.skills-section::before,
.value-section::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: rotate(45deg);
  animation: shimmer 6s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

/* 移动端优化 */
@media (max-width: 768px) {
  .page-nav {
    padding: 0.75rem 1rem;
  }

  .nav-title {
    font-size: 1.1rem;
  }

  .nav-btn {
    width: 36px;
    height: 36px;
  }

  .pet-details-content {
    padding: 1rem 0;
  }

  .pet-showcase {
    padding: 1.5rem;
    margin: 1rem;
  }

  .pet-display {
    height: 250px;
    margin-bottom: 1.5rem;
  }

  .pet-name {
    font-size: 1.75rem;
  }

  .pet-meta {
    gap: 0.5rem;
  }

  .pet-description {
    margin: 1rem 0;
    padding: 0.75rem;
  }

  .pet-description p {
    font-size: 0.9rem;
  }

  .pet-actions {
    gap: 0.75rem;
  }

  .stats-section,
  .status-section,
  .equipment-section,
  .skills-section,
  .value-section {
    padding: 1.25rem;
    margin: 0 1rem 2rem;
  }

  .container {
    padding: 0 1rem;
  }

  .section-title {
    font-size: 1.5rem;
    margin-bottom: 1rem;
  }

  /* 确保组件内容在移动端能够完整显示 */
  :deep(.pet-stats-panel),
  :deep(.pet-status-monitor),
  :deep(.pet-equipment-manager),
  :deep(.pet-skills-traits),
  :deep(.pet-value-assessment) {
    width: 100%;
    overflow-x: auto;
  }

  /* 确保表格和列表在移动端可以水平滚动 */
  :deep(table),
  :deep(.equipment-list),
  :deep(.skills-list) {
    min-width: 100%;
    width: max-content;
  }

  /* 调整按钮大小和间距 */
  .primary-btn,
  .secondary-btn {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }

  .pet-actions {
    gap: 0.5rem;
  }

  /* 减小萌宠展示区域的大小 */
  .pet-display {
    height: 250px;
    margin-bottom: 1.5rem;
  }

  .pet-name {
    font-size: 1.75rem;
  }
}

@media (max-width: 480px) {
  .page-nav {
    padding: 0.5rem 0.75rem;
  }

  .nav-title {
    font-size: 1rem;
  }

  .nav-btn {
    width: 32px;
    height: 32px;
  }

  .pet-showcase {
    padding: 1rem;
    margin: 0.5rem;
  }

  .pet-display {
    height: 200px;
    margin-bottom: 1rem;
  }

  .pet-name {
    font-size: 1.5rem;
  }

  .pet-meta {
    gap: 0.5rem;
  }

  .pet-type,
  .pet-rarity,
  .pet-level {
    font-size: 0.75rem;
    padding: 0.3rem 0.8rem;
  }

  .pet-description {
    margin: 0.75rem 0;
    padding: 0.5rem;
  }

  .pet-description p {
    font-size: 0.85rem;
  }

  .pet-actions {
    gap: 0.5rem;
  }

  .stats-section,
  .status-section,
  .equipment-section,
  .skills-section,
  .value-section {
    padding: 1rem;
    margin: 0 0.5rem 1.5rem;
  }

  .section-title {
    font-size: 1.25rem;
  }

  .primary-btn,
  .secondary-btn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }
}

/* 空状态 */
.empty-state {
  padding: 4rem 0;
  text-align: center;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
}

.empty-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 0.75rem;
}

.empty-desc {
  font-size: 1.125rem;
  color: #6e6e73;
  margin-bottom: 2rem;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.loading-spinner {
  width: 3rem;
  height: 3rem;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top-color: white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

.loading-text {
  color: white;
  font-size: 1.125rem;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 自定义弹窗样式 */
:deep(.custom-dialog) {
  border-radius: 18px;
  overflow: hidden;
}

:deep(.custom-popup) {
  border-radius: 18px 18px 0 0;
  overflow: hidden;
}

/* 响应式调整 */
@media (min-width: 768px) {
  .page-title {
    font-size: 3rem;
  }

  .pet-showcase {
    flex-direction: row;
    align-items: flex-start;
    gap: 3rem;
  }

  .pet-display {
    margin-bottom: 0;
  }

  .pet-info {
    text-align: left;
  }

  .pet-name-section,
  .pet-meta,
  .pet-actions {
    justify-content: flex-start;
  }
}

@media (min-width: 1024px) {
  .pet-details-content .container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
  }

  .pet-showcase {
    grid-column: span 2;
  }
}

/* 平板设备优化 */
@media (min-width: 768px) and (max-width: 1023px) {
  .pet-details-content .container {
    display: flex;
    flex-direction: column;
  }

  /* 在平板上使用单列布局，但保持较大的内容宽度 */
  .stats-section,
  .status-section,
  .equipment-section,
  .skills-section,
  .value-section {
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
  }
}
</style>
